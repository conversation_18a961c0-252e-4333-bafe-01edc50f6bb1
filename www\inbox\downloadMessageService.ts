import { AuthenticationService } from "../authenticationService";
import { HttpConnection } from "../helper/httpConnection";
import { LogLevel } from "../helper/settingsHelper";
import { Status } from "../helper/status";
import { UnviredAccountManager } from "../helper/unviredAccountManager";
import { isServerReachable, ObjectStatus, SyncStatus } from "../helper/utils";
import * as ServiceConstants from '../helper/serviceConstants'
import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import FrameworkHelper from "../helper/frameworkHelper";
import SettingsHelper from "../helper/settingsHelper";
import InboxHelper from "./inboxHelper";
import InboxService from "./inboxService";
import { NotificationListenerHelper, NotificationListnerType } from "../helper/notificationListnerHelper";

declare var Logger:any;

const fileName = "DownloadMessageService"
export default class DownloadMessageService {
    private static instance: DownloadMessageService;
    private isRunning: boolean = false;
    private shouldStop: boolean = false;

    private constructor() {}

    public static getInstance(): DownloadMessageService {
        if (!DownloadMessageService.instance) {
            DownloadMessageService.instance = new DownloadMessageService();
        }
        return DownloadMessageService.instance;
    }

    public start(): void {
        if (FrameworkHelper.getPlatform() === 'browser') {
            Logger.logInfo(fileName, "start", "Download message service is not supported in browser.");
            return;
        }
        if (this.isRunning) {
            Logger.logInfo(fileName, "start", "Download message service is already running.");
            return;
        }
        this.isRunning = true;
        this.shouldStop = false;
        this.checkAndDownloadMessages();
        Logger.logInfo(fileName, "start", "Download message service started.");
    }

    public stop(): void {
        if (this.isRunning) {
            this.shouldStop = true;
            Logger.logInfo(fileName, "stop", "Stopping download message service.");
        } else {
            Logger.logInfo(fileName, "stop", "Download message service is not running.");
        }
    }

    private async checkAndDownloadMessages(): Promise<void> {
        let downloadedMessagesCount = 0;
        try {
            Logger.logInfo(fileName, "checkAndDownloadMessages", "Starting download message service.")
            let continueDownloading = false;
            do {
                if (this.shouldStop) {
                    Logger.logInfo(fileName, "checkAndDownloadMessages", "Stop requested. Exiting message download loop.")
                    break;
                }
                if (!navigator.onLine) {
                    Logger.logError(fileName, "checkAndDownloadMessages", "Internet is not connected. Exitting message download loop.")
                    break;
                }
                const lastLoggedInAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
                if (!(await isServerReachable(lastLoggedInAccount.getServerURL()))) {
                    Logger.logError(fileName, "checkAndDownloadMessages", "Server is not reachable. Exitting message download loop.")
                    break;
                }

                Logger.logInfo(fileName, "checkAndDownloadMessages", "Fetching next message to download.")
                NotificationListenerHelper.postSynchronizationStateNotification(`receiving (${downloadedMessagesCount+1})`);
                continueDownloading = await this.downloadMessage();
                if (continueDownloading) {
                    downloadedMessagesCount++;
                }
            } while (continueDownloading);
            Logger.logInfo(fileName, "checkAndDownloadMessages", `No more message to download. Total messages downloaded: ${downloadedMessagesCount}`)
            if (downloadedMessagesCount > 0) {
                NotificationListenerHelper.postDataSenderNotification(null, NotificationListnerType.data_received);
            }
        } catch (e) {
            Logger.logError(fileName, "checkAndDownloadMessages", e.toString())
        } finally {
            Logger.logInfo(fileName, "checkAndDownloadMessages", "Stopping download message service.")
            this.isRunning = false;
            this.shouldStop = false;
            InboxService.getInstance().start()
        }
    }

    private async downloadMessage(): Promise<boolean> {
        let didDownloadMessages = false;
        try {
            Logger.logInfo(fileName, "downloadMessage", "Starting the main downloading message process.")
            const lastLoggedInAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
            Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `-------DOWNLOAD MESSAGE API CALL BEGIN------`)
            const result = await new HttpConnection().downloadMessage(lastLoggedInAccount, AuthenticationService.instance.loginParameters);
            Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `-------DOWNLOAD MESSAGE API CALL END------`)
            if (result.httpStatusCode === Status.ok) {
                try {
                    Logger.logInfo(fileName, "downloadMessage", "downloadMessageService result")
                    const responseData = JSON.parse(result.data);
                    const headerObj = JSON.parse(result.header);
                    const messageType = headerObj[ServiceConstants.HeaderConstantMessageType.toLowerCase()];
                    if (messageType != null &&
                        messageType == ServiceConstants.MESSAGE_TYPE_WIPE.toString()) {
                      const responseJson = {
                        "error":
                            "Deleted all the data for this application from device . Please exit the application by pressing the 'Home' button in your device.",
                        "systemError": 13
                      };
                      // TODO: Post Notification to the app
                      Logger.logError(fileName, "downloadMessage", "Deleted all the data for this application from device . Please exit the application by pressing the 'Home' button in your device.");
                      return false;
                    }

                    const conversationId = headerObj[ServiceConstants.HeaderConstantConversationId.toLowerCase()];
                    let requestType = headerObj[ServiceConstants.HeaderConstantRequestType.toLowerCase()];
                    const pendingMessageCount = headerObj[ServiceConstants.HeaderConstantNumberOfPendingMessages.toLowerCase()];
                    if (pendingMessageCount != null && pendingMessageCount > 0) {
                        NotificationListenerHelper.postSynchronizationStateNotification(`receiving (${pendingMessageCount})`);
                    }
                    let type = -1;
                    let subType = -1;
                    let appId = "";
                    let serverId = "";
                    let applicationName = "";

                    if (ServiceConstants.Type in responseData && ServiceConstants.Subtype in responseData) {
                        type = responseData[ServiceConstants.Type];
                        subType = responseData[ServiceConstants.Subtype];
                        appId = responseData[ServiceConstants.ApplicationId];
                        serverId = responseData[ServiceConstants.ServerId];
                        applicationName = responseData[ServiceConstants.AppName];
                    }

                    if (headerObj[ServiceConstants.HeaderConstantPullMode] === ServiceConstants.PullModeDelete) {
                        requestType = "PULL_D";
                    }

                    Logger.logInfo(fileName, "downloadMessage", `downloadMessageService result. TYPE: ${type}`)
                    Logger.logInfo(fileName, "downloadMessage", `downloadMessageService result. SUB_TYPE: ${subType}`)
                    const databaseManager = DatabaseManager.getInstance();
                    if (conversationId != null && requestType != null) {
                        const sentItems = await databaseManager.select(DatabaseType.FrameworkDb, "SentItems", `conversationId = '${conversationId}'`);
                        let entityName = "";
                        let beLid = "";
                        if (sentItems.length > 0) {
                            entityName = sentItems[0].beName;
                            beLid = sentItems[0].beHeaderLid;
                        }

                        const inObjectData: any = {
                            lid: FrameworkHelper.getUUID(),
                            timestamp: Date.now(),
                            objectStatus: ObjectStatus.global,
                            syncStatus: SyncStatus.none,
                            conversationId: conversationId,
                            requestType: requestType,
                            jsonData: entityName,
                            beLid: beLid,
                            subtype: subType,
                            serverId: serverId,
                            type: type,
                            appName: applicationName,
                            appId: appId
                        };

                        const inObjectsArray = await databaseManager.select(DatabaseType.FrameworkDb, "InObject", `conversationId = '${conversationId}'`)

                        if (inObjectsArray.length == 0) {
                            try {
                                await InboxHelper.addInBoxData(conversationId, JSON.stringify(responseData));
                            }
                            catch (e) {
                                Logger.logError(fileName, "downloadMessage", `Failed to add inbox data to file. Error: ${JSON.stringify(e)}`);
                                return false;
                            }
                            await databaseManager.insert(DatabaseType.FrameworkDb, "InObject", inObjectData, true);
                        }

                        // TODO: Post Notification to the app

                        Logger.logInfo(fileName, "downloadMessage", "acknowledgeMessageService")
                        Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `-------ACK MESSAGE API CALL BEGIN. CONV: ${conversationId}------`)
                        const response = await new HttpConnection().acknowledgeMessageService(conversationId);
                        Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `-------ACK MESSAGE API CALL END. CONV: ${conversationId}------`)

                        if (response.status !== Status.noContent) {
                            Logger.logInfo(fileName, "downloadMessage", `acknowledgeMessageService server return status code ${response.status}.`)
                            if (response.status === Status.gone) {
                                Logger.logError(fileName, "downloadMessage", `acknowledgeMessageService server return status code 410.`)
                                const responseObject = await response.json();
                                if (responseObject.systemError != null && responseObject.systemError === 13) {
                                    // TODO: Post Notification to the app
                                    return false;
                                }
                            }
                        }
                        didDownloadMessages = true;
                    }
                    else {
                        didDownloadMessages = false;
                    }
                } catch (e) {
                    Logger.logError(fileName, "downloadMessage", `downloadMessageService result - Failed ${JSON.stringify(e)}`)
                    return false;
                }
            } else if (result.httpStatusCode === Status.gone) {
                Logger.logError(fileName, "downloadMessage", `downloadMessageService result - Server return status code 410. Response: ${JSON.stringify(result.data)}`)
                const response = result.data;
                // TODO: Post Notification to the app
                return false;
            } else if (result.httpStatusCode === Status.noContent) {
                Logger.logInfo(fileName, "downloadMessage", "Nothing to download. Server return status code: 204")
                // TODO: Post Notification to the app
                return false;
            } else {
                Logger.logError(fileName, "downloadMessage", `downloadMessageService result - Failed ${JSON.stringify(result.data)}`)
                return false;
            }
        } catch (e) {
            if (e.name === 'AbortError') {
                await Logger.logError("HTTPConnection", "makeSyncCall", "Request timed out. Error: " + JSON.stringify(e));
            }
            else {
                Logger.logError(fileName, "downloadMessage", `downloadMessageService result - Failed ${JSON.stringify(e)}`)
            }
            return false;
        }
        Logger.logInfo(fileName, "downloadMessage", "Current message downloaded successfully. Proceed with next message download.")
        return true;
    }

    static async handleMessageTypes(inObject: any): Promise<void> {
        const messageType = inObject.type;
        const subType = inObject.subtype;
        switch (messageType) {
            case ServiceConstants.MESSAGE_TYPE_SYSTEM:
                await DownloadMessageService.systemMessageHandler(subType);
                return;
            default:
                return;
        }
    }

    static async systemMessageHandler(subType: number): Promise<void> {
        switch (subType) {
            case ServiceConstants.MESSAGE_SUBTYPE_SYSTEM_PING:
                await SettingsHelper.queuePingToOutbox();
                return;
            case ServiceConstants.MESSAGE_SUBTYPE_SYSTEM_LOG:
                await SettingsHelper.sendLogsToServer();
                return;
            case ServiceConstants.MESSAGE_SUBTYPE_SYSTEM_LOG_RESET:
                await SettingsHelper.deleteLogs();
                return;
            case ServiceConstants.MESSAGE_SUBTYPE_SYSTEM_LOG_SET_DEBUG:
                SettingsHelper.setLogLevel(LogLevel.debug);
                return;
            case ServiceConstants.MESSAGE_SUBTYPE_SYSTEM_LOG_SET_ERROR:
                SettingsHelper.setLogLevel(LogLevel.error);
                return;
            case ServiceConstants.MESSAGE_SUBTYPE_SYSTEM_DATA_DUMP:
                await SettingsHelper.sendAppDbToServer();
                return;
            default:
                // TODO: Post Notification to the app
                return;
        }
    }
}