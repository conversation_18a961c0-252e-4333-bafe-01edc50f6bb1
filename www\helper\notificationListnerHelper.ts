declare var Logger: any;

export const TYPE = "type";
export const DATA = "data";
export const MESSAGE = "message";
export const ERROR = "error";
export const ERROR_DETAIL = "errorDetail";

export const kAttachmentItem = "kAttachmentItem";
export const kError = "kError";

export enum NotificationListnerType {
    data_send = 0,
    data_changed = 1,
    data_received = 2,
    app_reset = 3,
    attachmentDownloadSuccess = 4,
    attachmentDownloadError = 5, // Notify application with error message and attchment item on attachment download success
    incomingDataProcessingFinished = 6,
    attachmentDownloadWaiting = 7, // Notify application when attachment download is waiting on the server
    infoMessage = 8, // Notify application with any InfoMessages
    serverError = 9,
    attachmentDownloadCompleted = 10,
    sentItemChanged = 11,
    jwtTokenUpdated = 12,
    jwtTokenExpired = 13
}

const fileName = "NotificationListenerHelper";

export class NotificationListenerHelper {
    public static dataSenderListener: any = null;
    public static synchronizationStateListener: any = null;
    public static postDataSenderNotification(data: any, notificationType: NotificationListnerType) {
        if (this.dataSenderListener === null) {
            Logger.logInfo(fileName, "postDataSenderNotification", "Data sender listener is null");
            return;
        }
        const notificationData = {
            [TYPE]: notificationType,
            [MESSAGE]: "",
            [DATA]: data ?? [],
            [ERROR]: "",
            [ERROR_DETAIL]: ""
        }
        this.dataSenderListener(notificationData);
    }

    public static postSynchronizationStateNotification(postMessage: string) {
        if (this.synchronizationStateListener === null) {
            Logger.logInfo(fileName, "postSynchronizationStateNotification", "Synchronization state listener is null");
            return;
        }
        this.synchronizationStateListener(postMessage);
    }
}