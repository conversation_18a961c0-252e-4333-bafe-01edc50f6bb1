import { StructureMeta } from "../applicationMeta/applicationMetadataParser";
import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import FrameworkHelper from "./frameworkHelper";
import { ObjectStatus, SyncStatus, isServerReachable } from "./utils";
import * as FieldConstants from "../applicationMeta/fieldConstants";
import * as ServiceConstants from "../helper/serviceConstants";
import { HttpConnection } from "./httpConnection";
import { AuthenticationService } from "../authenticationService";

declare var Logger: any;

export enum DataType { all, changed, changedQueued, queued }
class SyncInputDataManager {
    static async constructInputBeJson(entityName: string, dataObject: any): Promise<any> {
      if (Object.keys(dataObject).length === 0) {
        return {};
      }

      try {
        const databaseManager = DatabaseManager.getInstance();
        const structureMeta = (await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta"))
          .find((element: StructureMeta) => element.structureName === entityName);
        if (!structureMeta) {
          throw new Error(`StructureMeta not found for entityName: ${entityName}`);
        }
        const beMeta = (await databaseManager.select(DatabaseType.FrameworkDb, "BusinessEntityMeta"))
          .find((element: any) => element.beName === structureMeta.beName);
        if (!beMeta) {
          throw new Error(`BusinessEntityMeta not found for beName: ${structureMeta.beName}`);
        }

        if (beMeta.save != "true") {
            const inputObject = {
                [beMeta.beName]: [dataObject]
            };
            return inputObject;
        }

        const lid = dataObject[FieldConstants.FieldLid];
        if (!lid) {
            throw new Error(`${FieldConstants.FieldLid} is mandatory in the input data.`);
        }

        const whereClause = `${FieldConstants.FieldLid}='${lid}'`;
        const result = await databaseManager.select(DatabaseType.AppDb, entityName, whereClause);

        if (result.length === 0) {
          throw new Error(`Data not found for ${FieldConstants.FieldLid}: ${lid}.`);
        }
        const header = result[0];
        if (header[FieldConstants.FieldSyncStatus] === SyncStatus.queued) {
          throw new Error("Entity is in outbox. Cannot be submitted again.");
        }
        if (header[FieldConstants.FieldSyncStatus] === SyncStatus.sent) {
          throw new Error("Entity is in sent items waiting for a response from the server. Cannot be submitted again.");
        }

        const beObject: any = { [structureMeta.structureName]: header };
        const items = await this.getChildData(lid, structureMeta.beName, DataType.changed);
        Object.assign(beObject, items);
        const inputObject = {
          [beMeta.beName]: [beObject]
        };
        return inputObject;
      } catch (e) {
        await Logger.logError("SyncInputDataManager", "constructInputBeJson", JSON.stringify(e));
      }
      return {};
    }

    static async getChildData(lid: string, beName: string, dataType: DataType): Promise<any> {
        const databaseManager = DatabaseManager.getInstance();
        const result: any[] = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta")
        const childStructureMetas: StructureMeta[] = result.filter((element: StructureMeta) => element.beName === beName && element.isHeader !== "1");
        let whereClause = `${FieldConstants.FieldFid}='${lid}'`;
        switch (dataType) {
            case DataType.all:
                whereClause += "";
                break;
            case DataType.changed:
                whereClause += ` AND ${FieldConstants.FieldObjectStatus} IN (${ObjectStatus.add}, ${ObjectStatus.modify}, ${ObjectStatus.delete}) AND ${FieldConstants.FieldSyncStatus} IN (${SyncStatus.none}, ${SyncStatus.error})`;
                break;
            case DataType.changedQueued:
                whereClause += ` AND ${FieldConstants.FieldObjectStatus} IN (${ObjectStatus.add}, ${ObjectStatus.modify}, ${ObjectStatus.delete}) AND ${FieldConstants.FieldSyncStatus} IN (${SyncStatus.none}, ${SyncStatus.error}, ${SyncStatus.queued})`;
                break;
            case DataType.queued:
                whereClause += ` AND ${FieldConstants.FieldObjectStatus} IN (${ObjectStatus.add}, ${ObjectStatus.modify}, ${ObjectStatus.delete}) AND ${FieldConstants.FieldSyncStatus} IN (${SyncStatus.queued})`;
                break;
            default:
                break;
        }
        const childJsonObject: any = {};
        for (const structureMetaData of childStructureMetas) {
            try {
                const result: any[] = await databaseManager.select(DatabaseType.AppDb, structureMetaData.structureName, whereClause);
                childJsonObject[structureMetaData.structureName] = result;
            } catch (e) {
                Logger.logError("SyncInputDataManager", "_getChildData", JSON.stringify(e));
            }
        }
        return childJsonObject;
    }

    static async checkAndUploadAttachments(entityName: string, inputData: any, isAsynchronous: boolean = false): Promise<void> {
        const databaseManager = DatabaseManager.getInstance();
        const structureMetas = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta");
        const structureMeta = structureMetas.find((element: any) => element.structureName === entityName);
  
        // Invalid condition. Should never occur.
        if (!structureMeta) {
            await Logger.logError("SyncInputDataManager", "checkAndUploadAttachments",
                `Invalid BE. Cannot Upload Attachments. Table Name: ${entityName}`);
            return;
        }
  
        // If attachments are not supported do not do anything
        if (!await this.isAttachmentSupportedForBEName(structureMeta.beName)) {
            await Logger.logError("SyncInputDataManager", "checkAndUploadAttachments",
                `This BE: ${structureMeta.beName} does not support attachments.`);
            return;
        }
  
        try {
            const attachmentItemNames = structureMetas
                .filter((element: any) =>
                    element.beName === structureMeta.beName &&
                    element.structureName.endsWith(ServiceConstants.AttachmentBE))
                .map((e: any) => e.structureName);
  
            for (const attachmentItemName of attachmentItemNames) {
                const attachmentItems = await this.getAttachmentsMarkedForUpload(attachmentItemName, inputData);
                for (const attachmentItem of attachmentItems) {
                    try {
                        if (isAsynchronous) {
                            // Wait till the internet is connected.
                            // TODO: Implement this
                            // await ConnectivityManager.waitForConnection();
                        } else {
                            if (!navigator.onLine) {
                                break;
                            }
                            if (!(await isServerReachable(AuthenticationService.instance.loginParameters.url))) {
                                break;
                            }
                        }
                        const response = await new HttpConnection().uploadAttachment(attachmentItem);
                        if (response.status === 200 || response.status === 201) {
                            this.updateAttachmentStatus(
                                attachmentItemName, attachmentItem, ServiceConstants.AttachmentStatusUploaded);
                        } else {
                            await Logger.logError(
                                "SyncInputDataManager",
                                "checkAndUploadAttachments",
                                "Error while uploading attachment.");
  
                            const infoMessage = {
                                lid: FrameworkHelper.getUUID(),
                                timestamp: Date.now(),
                                objectStatus: ObjectStatus.global,
                                syncStatus: SyncStatus.none,
                                type: "",
                                subtype: "",
                                category: "InfoMessageFailure",
                                message: "Error while uploading attachment.",
                                bename: entityName,
                                belid: attachmentItem[FieldConstants.FieldLid],
                                messagedetails: ""
                            };
                            await databaseManager.insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessage, true);
                            this.updateAttachmentStatus(attachmentItemName, attachmentItem,
                                ServiceConstants.AttachmentStatusErrorInUpload);
                        }
                    } catch (e) {
                        await Logger.logError("SyncInputDataManager",
                            "checkAndUploadAttachments", JSON.stringify(e));
                        this.updateAttachmentStatus(attachmentItemName, attachmentItem,
                            ServiceConstants.AttachmentStatusErrorInUpload);
                    }
                }
            }
        } catch (e) {
            await Logger.logError(
                "SyncInputDataManager", "checkAndUploadAttachments", JSON.stringify(e));
        }
    }

    private static async updateAttachmentStatus(attachmentItemName: string, attachmentItem: any, attachmentStatus: string): Promise<boolean> {
        attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = attachmentStatus;
        return await DatabaseManager.getInstance().update(DatabaseType.AppDb, attachmentItemName, attachmentItem, attachmentItem[FieldConstants.FieldLid]);
    }

    static async isAttachmentSupportedForBEName(beName: string): Promise<boolean> {
        if (!beName) {
            return false;
        }
        try {
            const databaseManager = DatabaseManager.getInstance();
            const beMetas = await databaseManager.select(DatabaseType.FrameworkDb, "BusinessEntityMeta");
            const beMeta = beMetas.find((element: any) => element.beName === beName);
            return beMeta.attachments === "1";
        } catch (e) {
            await Logger.logError("SyncInputDataManager", "isAttachmentSupportedForBEName", JSON.stringify(e));
        }
        return false;
    }

    static async getAttachmentsMarkedForUpload(attachmentItemName: string, inputData: any): Promise<any[]> {
        const whereClause = `${FieldConstants.FieldFid}='${inputData[FieldConstants.FieldLid]}' AND ${ServiceConstants.AttachmentItemFieldAttachmentStatus}='${ServiceConstants.AttachmentStatusSavedForUpload}'`;
        const databaseManager = DatabaseManager.getInstance();
        const attachmentItems = await databaseManager.select(DatabaseType.AppDb, attachmentItemName, whereClause);
        return attachmentItems;
    }
}

export default SyncInputDataManager;