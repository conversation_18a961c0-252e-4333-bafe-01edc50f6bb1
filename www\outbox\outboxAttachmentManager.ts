import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import * as FieldConstants from '../applicationMeta/fieldConstants';
import * as ServiceConstants from '../helper/serviceConstants';
import { UnviredAccountManager } from "../helper/unviredAccountManager";
import { HttpConnection, bearerAuth } from "../helper/httpConnection";
import { UserSettingsFields, UserSettingsManager } from "../helper/userSettingsManager";
import { isServerReachable, LoginType, ObjectStatus, SyncStatus } from "../helper/utils";
import FrameworkHelper from "../helper/frameworkHelper";
import { Status } from "../helper/status";
import { AuthenticationService } from "../authenticationService";

declare var Logger: any;
export default class OutboxAttachmentManager {
    async checkAndUploadAttachmentsInOutBox(entityName:string, inputData: any): Promise<boolean> {
        const databaseManager = DatabaseManager.getInstance();
        const structureMetas = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta");
        const structureMeta = structureMetas.find(element => element.structureName === entityName);
        // Invalid condition. Should never occur.
        if (!structureMeta) {
            Logger.logError("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", `Invalid BE. Cannot Upload Attachments. Table Name: ${entityName}`);
            return false;
        }
        // If attachments are not supported do not do anything
        const isAttachmentSupported: boolean = await this.isAttachmentSupportedForBEName(structureMeta.beName);
        if (!isAttachmentSupported) {
            Logger.logInfo("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", `This BE: ${structureMeta.beName} does not support attachments.`);
            return true; // Return true since no attachments to upload
        }

        Logger.logDebug("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", `This BE: ${structureMeta.beName} supports attachments.`);

        try {
            const attachmentEntityNames: string[] = structureMetas
                .filter(element => element.beName === structureMeta.beName && element.structureName.endsWith(ServiceConstants.AttachmentBE))
                .map(e => e.structureName);

            for (const attachmentEntityName of attachmentEntityNames) {
                Logger.logInfo("OutboxAttachmentManager", "makeNetworkCallToDownloadAttachment", `-------GETTING ATTACHMENTS TO UPLOAD - ATTACHMENT ENTITY NAME:${attachmentEntityName}------`)
                let attachmentItems: any[] = [];
                try {
                    attachmentItems = await this.getAttachmentsMarkedForUploadOutbox(attachmentEntityName, inputData);
                } catch (e) {
                    Logger.logError("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", `Error when trying to get attachments marked for upload ${JSON.stringify(e)}`);
                    return false;
                }
                Logger.logInfo("OutboxAttachmentManager", "makeNetworkCallToDownloadAttachment", `-------NO OF ATTACHMENTS TO UPLOAD: ${attachmentItems.length}------`)
                for (const attachmentItem of attachmentItems) {
                    try {
                        if (!navigator.onLine) {
                            Logger.logError("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", "No Internet connection.");
                            return false;
                        }
                        const gUid = attachmentItem[ServiceConstants.AttachmentItemFieldUid];
                        Logger.logInfo("OutboxAttachmentManager", "makeNetworkCallToDownloadAttachment", `-------UPLOADING ATTACHMENT STATRTS - UID:${gUid}------`)
                        const lastLoggedInAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
                        if (!(await isServerReachable(lastLoggedInAccount.getServerURL()))) {
                            Logger.logInfo("OutboxAttachmentManager", "makeNetworkCallToDownloadAttachment", `-------UPLOADING ATTACHMENT STOPPED BECAUSE OF SERVER NOT REACHABLE - UID:${gUid}------`)
                            return false;
                        }
                        const response = await new HttpConnection().uploadAttachment(attachmentItem);
                        if (response.status === Status.ok || response.status === Status.created) {
                            Logger.logInfo("OutboxAttachmentManager", "makeNetworkCallToDownloadAttachment", `-------ATTACHMENT UPLOADED SUCCESSFULLY - UID:${gUid}------`)

                            const isSuccess: boolean = await this.updateAttachmentStatusOutBox(attachmentEntityName, attachmentItem, ServiceConstants.AttachmentStatusUploaded);
                            if (!isSuccess) {
                                Logger.logError("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", `Failed to update status in ${attachmentEntityName} LID : ${attachmentItem[FieldConstants.FieldLid]}`);
                            }
                            Logger.logInfo("OutboxAttachmentManager", "makeNetworkCallToDownloadAttachment", `-------UPDATED ATTACHMENT STATUS SUCCESSFULLY - UID:${gUid}------`)
                            return true;
                        } else {
                            Logger.logInfo("OutboxAttachmentManager", "makeNetworkCallToDownloadAttachment", `-------ERROR WHILE UPLOADING ATTACHMENT - UID:${gUid}------`)
                            Logger.logError("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", "Error while uploading attachment.");  
                            const infoMessage = {
                                lid: FrameworkHelper.getUUID(),
                                timestamp: Date.now(),
                                objectStatus: ObjectStatus.global,
                                syncStatus: SyncStatus.none,
                                type: "",
                                subtype: "",
                                category: 'InfoMessageFailure',
                                message: "Error while uploading attachment.",
                                bename: entityName,
                                belid: attachmentItem[FieldConstants.FieldLid],
                                messagedetails: ""
                            };
                            await databaseManager.insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessage, true);
                            const isSuccess: boolean = await this.updateAttachmentStatusOutBox(attachmentEntityName, attachmentItem, ServiceConstants.AttachmentStatusErrorInUpload);
                            if (!isSuccess) {
                                Logger.logError("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", `Failed to update status in ${attachmentEntityName} LID : ${attachmentItem[FieldConstants.FieldLid]}`);
                            }
                            Logger.logInfo("OutboxAttachmentManager", "makeNetworkCallToDownloadAttachment", `-------ERROR WHILE UPLOADING ATTACHMENT. UPDATED INFO MESSAGE - UID:${gUid}------`)
                            return false;
                        }
                    } catch (e) {
                        const isSuccess: boolean = await this.updateAttachmentStatusOutBox(attachmentEntityName, attachmentItem, ServiceConstants.AttachmentStatusErrorInUpload);
                        if (!isSuccess) {
                            Logger.logError("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", `Failed to update status in ${attachmentEntityName} LID : ${attachmentItem[FieldConstants.FieldLid]}`);
                        }
                        return false;
                    }
                }
            }
            return true;
        } catch (e) {
            Logger.logError("OutboxAttachmentManager", "checkAndUploadAttachmentsInOutBox", `Error when trying to check and upload attachments in outbox ${JSON.stringify(e)}`);
            return false;
        }
    }

    async updateAttachmentStatusOutBox(attachmentItemName: string, attachmentItem: any, attachmentStatus: string): Promise<boolean> {
        attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = attachmentStatus;
        const databaseManager = DatabaseManager.getInstance();
        return await databaseManager.update(DatabaseType.AppDb, attachmentItemName, attachmentItem, `${FieldConstants.FieldLid} = '${attachmentItem[FieldConstants.FieldLid]}'`);
    }

    async isAttachmentSupportedForBEName(beName: string): Promise<boolean> {
        if (!beName) {
            return false;
        }
        try {
            const databaseManager = DatabaseManager.getInstance();
            const beMeta = await databaseManager.select(DatabaseType.FrameworkDb, "BusinessEntityMeta", `beName = '${beName}'`)
            if (beMeta.length === 0) {
                return false;
            }
            return beMeta[0].attachments === "1";
        } catch (e) {
            Logger.logError("OutboxAttachmentManager", "isAttachmentSupportedForBEName", `Error when trying to get BusinessEntityMeta. Error: ${JSON.stringify(e)}`);
        }
        return false;
    }

    async getAttachmentsMarkedForUploadOutbox(attachmentEntityName: string, inputData: any): Promise<any[]> {
        const parsedData: any = JSON.parse(inputData[ServiceConstants.QueryParamInputMessage]);
        const inputDataKeys: Iterable<string> = Object.keys(parsedData);
        const beData: any = parsedData[inputDataKeys[0]];

        let headerData: any ;
        for (const key in beData[0]) {
            if (key.endsWith("_HEADER")) {
                headerData = beData[0][key];
                break;
            }
        }
        if (!headerData) {
            Logger.logError("OutboxAttachmentManager", "getAttachmentsMarkedForUploadOutbox", "Header data not available.");
            return [];
        }
        // Check if the header supports attachments, if yes, check if it has any, if yes send to server.
        const whereClause: string = `${FieldConstants.FieldFid} = '${headerData[FieldConstants.FieldLid]}' AND ${ServiceConstants.AttachmentItemFieldAttachmentStatus} = '${ServiceConstants.AttachmentStatusSavedForUpload}'`;
        let attachmentItems: any[] = [];
        try {
            const databaseManager = DatabaseManager.getInstance();
            attachmentItems = await databaseManager.select(DatabaseType.AppDb, attachmentEntityName, whereClause);
        } catch (e) {
            Logger.logError("OutboxAttachmentManager", "getAttachmentsMarkedForUploadOutbox", `Error when trying to get attachments marked for upload ${JSON.stringify(e)}`);
        }

        return attachmentItems;
    }
}