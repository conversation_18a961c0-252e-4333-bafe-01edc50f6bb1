import * as CryptoJS from 'crypto-js';
import * as base32 from 'hi-base32';
const fileName = "PassCodeGenerator"

declare var Logger: any;;

class PassCodeGenerator {
  private static readonly PASS_CODE_LENGTH: number = 6;
  private static readonly INTERVAL: number = 30;
  private static readonly ADJACENT_INTERVALS: number = 5;
  private static readonly PIN_MODULO: number = Math.pow(10, PassCodeGenerator.PASS_CODE_LENGTH);

  computePin(secret: string | null, feUserId: string | null): string[] {
    const timeStamp = this.getTimeStamp();
    if (!secret || secret.length === 0) {
      return [timeStamp];
    }

    try {
      const keyBytes = base32.decode.asBytes(secret.toUpperCase()); 
      const keyWordArray = CryptoJS.lib.WordArray.create(keyBytes);
      const hmacSha1 = CryptoJS.HmacSHA1(feUserId + '~' + timeStamp, keyWordArray);

      const hashBytes = CryptoJS.enc.Hex.parse(hmacSha1.toString());
      let offset = parseInt(hashBytes.toString(CryptoJS.enc.Hex).slice(-1), 16) & 0xf;

      let result = 0;
      for (let i = 0; i < 4; ++i) {
        result = (result << 8) + parseInt(hashBytes.toString(CryptoJS.enc.Hex).substr(offset * 2, 2), 16);
        offset++;
      }
      const truncatedHash = result & 0x7fffffff;
      const pinValue = truncatedHash % PassCodeGenerator.PIN_MODULO;
      const resultPin = this.padOutput(pinValue);
      return [resultPin, timeStamp];
    } catch (e) {
      Logger.logError(fileName, "computePin", "Error while generating pin. Error: " + JSON.stringify(e));
      throw e;
    }
  }

  getTimeStamp(): string {
    const currentDate = new Date();
    const utcDate = new Date(currentDate.getTime() + currentDate.getTimezoneOffset() * 60000)
    const year = utcDate.getUTCFullYear();
    const month = String(utcDate.getUTCMonth() + 1).padStart(2, '0');
    const day = String(utcDate.getUTCDate()).padStart(2, '0');
    const hours = String(utcDate.getUTCHours()).padStart(2, '0');
    const minutes = String(utcDate.getUTCMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  padOutput(value: number): string {
    let result = value.toString();
    while (result.length < PassCodeGenerator.PASS_CODE_LENGTH) {
      result = '0' + result;
    }
    return result;
  }
}

export default new PassCodeGenerator();