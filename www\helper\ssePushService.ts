import FrameworkHelper from "./frameworkHelper";
import { UnviredAccountManager } from "./unviredAccountManager";
import { UnviredAccount } from './unviredAccount';
import HttpConnection from "./httpConnection";
import SyncEngine from "../syncEngine";
import { NotificationConstants } from "./notificationConstants";

declare var Logger: any;

class SsePushService {
    private static eventSource: EventSource | null = null;

    private static getEventSource(url: string): EventSource {
        if (SsePushService.eventSource) {
            SsePushService.eventSource.close();
        }
        SsePushService.eventSource = new EventSource(url);
        return SsePushService.eventSource;
    }

    static async startPushService(): Promise<void> {
        const lastLoggedInAccount: UnviredAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
        if (lastLoggedInAccount == null) {
            return;
        }
        const sessionId: string = FrameworkHelper.getUUID();

        try {
            const result = await HttpConnection.registerNotification(sessionId);
            if (!result.ok) {
                Logger.logError("SsePushService", "startPushService", "Error: " + JSON.stringify(result));
                return;
            }
        } catch (error) {
            Logger.logError("SsePushService", "startPushService", "Error: " + JSON.stringify(error));
            return;
        }

        // localStorage.setItem("pushSessionId", sessionId);
        const apiToken: string = lastLoggedInAccount.getJwtToken();
        const baseUrl: string = lastLoggedInAccount.getServerURL();
        const url: string = `${baseUrl}/push?apiToken=${apiToken}&sessionId=${sessionId}`;
        const es: EventSource = SsePushService.getEventSource(url);
        es.onopen = (ev: Event) => {
            console.log('Connection to server opened.', ev);
        };
        es.onmessage = (event: MessageEvent) => {
            console.log(event);
            console.log(JSON.stringify(event));
            if (event.data == null || event.data == undefined || event.data.length === 0 || event.data === "ping") {
                return;
            }
            const pushData = JSON.parse(event.data);
            if (pushData[NotificationConstants.TYPE] != NotificationConstants.SYSTEM && pushData[NotificationConstants.TYPE] != NotificationConstants.USER) {
                return;
            }
            // handle event.data as needed
            SyncEngine.getMessages();
        };
        es.onerror = (error: Event) => {
            console.error('EventSource failed:', error);
        };
    }
}

export default SsePushService;