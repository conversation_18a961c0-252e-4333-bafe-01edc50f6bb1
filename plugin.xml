<?xml version='1.0' encoding='utf-8'?>
<plugin id="cordova-plugin-unvired-universal-sdk" version="1.1.1" xmlns="http://apache.org/cordova/ns/plugins/1.0" xmlns:android="http://schemas.android.com/apk/res/android">
  <name>UnviredSDK</name>
  <dependency id="cordova-plugin-unvired-device" version="^1.0.4" />
  <dependency id="cordova-plugin-unvired-electron-db" version="^0.0.27" />
  <!-- <dependency id="cordova-plugin-unvired-file" version="^1.0.2"/> -->
  <dependency id="cordova-plugin-unvired-logger" version="^0.0.13" />
  
  <js-module name="UnviredSDK" src="www/kernel.js">
    <clobbers target="window.ump" />
  </js-module>
</plugin>
