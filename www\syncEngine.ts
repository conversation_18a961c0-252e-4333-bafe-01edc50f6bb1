import * as FieldConstants from "./applicationMeta/fieldConstants";
import DatabaseManager, { DatabaseType } from "./database/databaseManager";
import FrameworkHelper from "./helper/frameworkHelper";
import { ObjectStatus, SyncStatus, isServerReachable } from "./helper/utils";
import  * as ServiceConstants  from './helper/serviceConstants';
import { UnviredAccountManager } from "./helper/unviredAccountManager";
import { AuthenticationService, ResultType, SyncResult } from "./authenticationService";
import { OutObjectStatus, RequestType, SyncType } from "./helper/serviceConstants";
import { FrameworkSettingsManager } from "./helper/frameworkSettingsManager";
import { HttpConnection } from "./helper/httpConnection";
import { Status } from "./helper/status";
import SyncInputDataManager from "./helper/syncInputDataManager";
import SettingsHelper from "./helper/settingsHelper";
import ServerResponseHandler from "./helper/serverResponseHandler";
import OutBoxHelper from "./outbox/outboxHelper";
import DownloadMessageService from "./inbox/downloadMessageService";
import OutboxService from "./outbox/outboxService";
import InboxService from "./inbox/inboxService";

declare var Logger: any;
export class SyncEngine {

    public async syncForeground(reqType: RequestType, header: any, customData: string, paFunction: string, autoSave: boolean): Promise<any> {
        return new Promise(async (resolve, reject) => {
            try {
                const result = await this.handleSend(reqType, header, customData, paFunction, autoSave, false);
                if (result.type === ResultType.error) {
                    Logger.logError("SyncEngine", "syncForeground", `Error: ${JSON.stringify(result)}`);
                }
                else {
                    Logger.logDebug("SyncEngine", "syncForeground", `Result: ${JSON.stringify(result)}`);
                }
                resolve(result);
            } catch (e) {
                Logger.logError("SyncEngine", "syncForeground", `Error: ${JSON.stringify(e)}`);
                reject(e);
            }
        });
    }

    public async syncBackground(reqType: RequestType, header: any, customData: string, paFunction: string, beName: string): Promise<any> {
        return new Promise(async (resolve, reject) => {
            try {
                // TODO: check why beName and beLid are required. And check if bypassAttachment is required.
                const result = await this.handleSend(reqType, header, customData, paFunction, true, true);
                Logger.logDebug("SyncEngine", "syncBackground", `Result: ${JSON.stringify(result)}`);
                resolve(result);
            } catch (e) {
                Logger.logError("SyncEngine", "syncBackground", `Error: ${JSON.stringify(e)}`);
                reject(e);
            }
        });
    }

    public getSynchronizationState(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public startInboxHandler(): Promise<any> {
        return new Promise((resolve, reject) => {
            InboxService.getInstance().start();
            resolve(true);
        });
    }

    public startDataSender(): Promise<any> {
        return new Promise((resolve, reject) => {
            OutboxService.getInstance().start();
            resolve(true);
        });
    }
  
    public getMessages(): Promise<any> {
        return new Promise((resolve, reject) => {
            DownloadMessageService.getInstance().start();
            resolve(true);
        });
    }
  
    public registerNotifListener(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public unRegisterNotifListener(): Promise<void> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                resolve();
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
  
    public isInOutbox(beLid: string): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public outBoxItemCount(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public isInSentItem(beLid: string): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public sentItemCount(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public inBoxItemCount(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
  
    public deleteOutBoxEntry(beLid: string): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
  
    public registerForPushNotification(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
  
    public resetApplicationSyncData(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
  
    public generateUBJson(headerName: string, header: any, itemName: string, items: any[]): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public parseRawUBJson(json: string): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
  
    public lockDataSender(beLid: string): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = {
                    type: ResultType.success,
                    data: 2
                 };
                resolve(result);
            } else {
                const error = {
                    type: ResultType.error,
                    error: "Failed to lock data sender."
                };
                reject(error);
            }
        });
    }
    public unlockDataSender(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = {
                    type: ResultType.success,
                    data: {}
                 };
                resolve(result);
            } else {
                const error = {
                    type: ResultType.error,
                    error: "Failed to unlock data sender."
                };
                reject(error);
            }
        });
    }

    public removeOutObjectBasedOnLid(lid: string): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public refreshJWTToken(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
    
    private async handleSend(requestType: RequestType, header:any, customData: any, paName: string, autoSave: boolean, isAsynchronous: boolean): Promise<SyncResult> {
        const selectedAccount = AuthenticationService.instance.loginParameters;
        let syncResult = new SyncResult();
        if (selectedAccount.isDemoLogin) {
            syncResult.code = 201;
            syncResult.type = ResultType.success;
            syncResult.message = 'Demo login successful.'
            return syncResult;
        }

        if (!paName) {
            syncResult.type = ResultType.error;
            syncResult.error = 'Process agent function name is mandatory.'
            syncResult.message = 'Process agent function name is mandatory.'
            syncResult.code = 0;
            return syncResult;
        }

        // TODO:
        // if (!this.lockedLid) {
        //     await Logger.logDebug("SyncEngine", "handleSend", "NO BE LOCKED");
        // } else {
        //     await Logger.logDebug("SyncEngine", "handleSend", `BE LOCKED ${this.lockedLid}`);
        // }

        if (requestType !== RequestType.PULL && (header != null && header != '' && Object.keys(header).length === 0) && (customData != null && customData != '' && Object.keys(customData).length === 0)) {
            syncResult.type = ResultType.error;
            syncResult.error = 'Invalid Input data.'
            syncResult.message = 'Invalid Input data.'
            syncResult.code = 0;
            return syncResult;
        }
        let inputDataObject = {}
        let entityName = "";
        if (header != null && Object.keys(header).length > 0) {
            const allKeys = Object.keys(header)
            entityName = allKeys.length > 0 ? allKeys[0] : "";
            inputDataObject = header[entityName]
        }
        else if (customData != null && Object.keys(customData).length > 0) {
            if (typeof customData === "string") {
                inputDataObject = customData
            } else {
                inputDataObject = JSON.parse(JSON.stringify(customData))
                const allKeys = Object.keys(inputDataObject)
                for (const key of allKeys) {
                    const value = inputDataObject[key]
                    if (value instanceof Array) {
                        const subKeys = Object.keys(value[0])
                        if (subKeys.length > 0) {
                            const headerNames = subKeys.filter(subKey => subKey.endsWith("_HEADER"))
                            if (headerNames.length > 0) {
                                entityName = headerNames[0]
                                break;
                            }
                        }
                        else {
                            break;
                        }
                    }
                    else {
                        break;
                    }
                }
            }
        }
        if (requestType === RequestType.RQST && (!entityName || entityName.length === 0)) {
            syncResult.type = ResultType.error;
            syncResult.error = 'Invalid Input data.'
            syncResult.message = 'Invalid Input data.'
            syncResult.code = 0;
            return syncResult;
        }
        await Logger.logInfo("SyncEngine", "handleSend", `inputDataObject: ${JSON.stringify(inputDataObject)}`);
        const isSensitive = false
        const isCustomInput = customData != null && customData.toString().length > 0 && Object.keys(customData).length > 0


        if (isAsynchronous) {
            await Logger.logDebug("SyncEngine", "handleSend", `making async call ${paName}`);
            const result = await this.sendAsync(requestType, inputDataObject, entityName, paName, isCustomInput, isSensitive);
            return result;
        } else {
            await Logger.logDebug("SyncEngine", "handleSend", `making sync call ${paName}`);
            try {
                let inputJson = inputDataObject;
                if (header != null && Object.keys(header).length > 0) {
                    if (requestType === RequestType.REQ || requestType === RequestType.RQST) {
                        if (FrameworkHelper.getPlatform() !== "browser") {
                            await SyncInputDataManager.checkAndUploadAttachments(entityName, inputJson);
                        }
                    }
                    inputJson = await SyncInputDataManager.constructInputBeJson(entityName, inputDataObject);
                }
                const dataString = typeof inputJson === "string"
                    ? inputJson
                    : (Object.keys(inputJson).length === 0 ? "" : JSON.stringify(inputJson));
                const inputDataString = await this.constructRequestString(requestType, dataString, isCustomInput, isAsynchronous, isSensitive);
                if (!navigator.onLine) {
                    await Logger.logDebug("SyncEngine", "handleSend", "Internet is not connected.");
                    // throw("Internet is not connected.");
                    syncResult.type = ResultType.error;
                    syncResult.error = 'Internet is not connected.'
                    syncResult.message = 'Internet is not connected.'
                    syncResult.code = 0;
                    return syncResult;
                }
                
                if (!(await isServerReachable(AuthenticationService.instance.loginParameters.url))) {
                    await Logger.logDebug("SyncEngine", "handleSend", "Server not reachable.");
                    // throw("Server not reachable.");
                    syncResult.type = ResultType.error;
                    syncResult.error = 'Server not reachable.'
                    syncResult.message = 'Server not reachable.'
                    syncResult.code = 0;
                    return syncResult;
                }
                const unviredResult = new SyncResult();
                try {
                    const lastLoggedInAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
                    const result = await new HttpConnection().makeSyncCall(inputDataString, paName, lastLoggedInAccount, AuthenticationService.instance.loginParameters);
                    let responseObject = JSON.parse(result.data ?? "");
                    await Logger.logDebug("SyncEngine", "handleSend", `ResponseJson: ${JSON.stringify(responseObject)}`);
                    await Logger.logDebug("SyncEngine", "handleSend", `Response: ${JSON.stringify(responseObject)}`);
                    unviredResult.code = result.httpStatusCode;
                    if (result.httpStatusCode === Status.ok || result.httpStatusCode === Status.created) {
                        unviredResult.type = ResultType.success;
                        unviredResult.data = responseObject;
                        const copiedResponse = JSON.parse(JSON.stringify(responseObject));
                        if (result.header != null && result.header[ServiceConstants.HeaderConstantPullMode] && result.header[ServiceConstants.HeaderConstantPullMode] === ServiceConstants.PullModeDelete) {
                            requestType = RequestType.PULLD;
                        }
                        const dataHandlerResult:any = await ServerResponseHandler.handleResponseData(copiedResponse || {}, autoSave, true, inputDataObject[FieldConstants.FieldLid] || "", entityName, requestType);
                        if (dataHandlerResult != null && dataHandlerResult != undefined) {
                            unviredResult.type = dataHandlerResult.type;
                            unviredResult.message = dataHandlerResult.message;
                        }
                    } 
                    else if (result.httpStatusCode === Status.noContent) {
                        unviredResult.type = ResultType.success;
                        unviredResult.data = "";
                    }
                    else {
                        unviredResult.type = ResultType.error;
                        if (responseObject["error"] != null && responseObject["error"] != undefined) {
                            unviredResult.error = responseObject["error"];
                        } else {
                            unviredResult.error = responseObject;
                        }
                        if (responseObject["message"] != null && responseObject["message"] != undefined) {
                            unviredResult.message = responseObject["message"];
                        } else {
                            unviredResult.message = responseObject;
                        }
                        if (responseObject["systemError"] && responseObject["systemError"] === 13) {
                            unviredResult.error = responseObject["error"]
                            unviredResult.message = responseObject["error"]
                            Logger.logError("SyncEngine", "handleSend", `Error: ${JSON.stringify(responseObject)}`);
                            await AuthenticationService.instance.clearData();
                        } else {
                            await this.handleInfoMessage(responseObject || {}, inputDataObject[FieldConstants.FieldLid] || "", entityName);
                        }
                    }
                }
                catch (error) {
                    await Logger.logError("SyncEngine", "handleSend", `Error: ${JSON.stringify(error)}`);
                    unviredResult.type = ResultType.error;
                    unviredResult.error = error;
                    unviredResult.message = error;
                }
                return unviredResult
            } catch (e) {
                if (e.name === 'AbortError') {
                    await Logger.logError("SyncEngine", "handleSend", `Timeout Exception: ${JSON.stringify(e)}`);
                    // throw("Connection timed out. Please check your internet connection and try again. If the issue persists, contact your administrator.");
                    syncResult.type = ResultType.error;
                    syncResult.error = 'Connection timed out. Please check your internet connection and try again. If the issue persists, contact your administrator.'
                    syncResult.message = 'Connection timed out. Please check your internet connection and try again. If the issue persists, contact your administrator.'
                    syncResult.code = 0;
                    return syncResult;
                } else {
                    // throw e.message;
                    syncResult.type = ResultType.error;
                    syncResult.error = e.message;
                    syncResult.message = e.message;
                    syncResult.code = 0;
                    return syncResult;
                }
            }
        }
    }

    private async constructRequestString(requestType:RequestType, dataString: string, isCustomInput: boolean, isAsynchronous: boolean, isSensitive: boolean): Promise<string> {
        const account = AuthenticationService.instance.loginParameters
        if (!account) {
            await Logger.logError("SyncEngine", "constructRequestString", "Account data not available.");
            throw("Account data not available.");
        }

        const bodyObject: { [key: string]: string } = {
            [ServiceConstants.QueryParamFrontendUser]: account.feUserId
        };

        if (dataString.length > 0) {
            bodyObject[ServiceConstants.QueryParamInputMessage] = dataString;
        }

        if (requestType.length > 0) {
            bodyObject[ServiceConstants.QueryParamRequestType] = requestType;
        }

        if (isCustomInput) {
            bodyObject[ServiceConstants.QueryParamMessageFormat] = ServiceConstants.MessageTypeCustom;
        }
        
        if (isAsynchronous) {
            const externalRef = FrameworkHelper.getUUID();
            bodyObject[ServiceConstants.QueryParamQueuedExecute] = "true";
            bodyObject[ServiceConstants.QueryParamExternalReference] = externalRef;
        }

        if (isSensitive) {
            bodyObject[ServiceConstants.QueryParamSensitive] = "true";
        }

        const systemCredentials = await FrameworkSettingsManager.getInstance().getAllSystemCredentials();
        if (systemCredentials.length > 0) {
            var credDetails = [];
            for (const cred of systemCredentials) {
                if (cred.portName && cred.userId && cred.password) {
                    const credDetail = {
                        port: cred.portName,
                        user: cred.userId,
                        password: cred.password
                    };
                    credDetails.push(credDetail);
                }
            }
            if (credDetails.length > 0) {
                bodyObject[ServiceConstants.QueryParamCredentials] = JSON.stringify(credDetails);
            }
        }

        const combined = { ...bodyObject, ...(await HttpConnection.getCommonPostParameters()) };

        return JSON.stringify(combined);
    }

    private async sendAsync(requestType:RequestType, dataObject: any, entityName: string, paName: string, isCustomInput: boolean, isSensitive: boolean): Promise<SyncResult> {
        let syncResult = new SyncResult();
        try {
            let inputJson = dataObject;
            if (!isCustomInput) {
                inputJson = await SyncInputDataManager.constructInputBeJson(entityName, dataObject);
            }
            const isAsyncCall = FrameworkHelper.getPlatform() !== "browser";
            const dataString = typeof inputJson === "string"
                ? inputJson
                : (Object.keys(inputJson).length === 0 ? "" : JSON.stringify(inputJson));
            const inputDataString = await this.constructRequestString(requestType, dataString, isCustomInput, isAsyncCall, isSensitive);

            switch (requestType) {
                // case RequestType.PULL:
                // case RequestType.QUERY:
                //     if (Object.keys(dataObject).length > 0 && !entityName) {
                //         await Logger.logError("SyncEngine", "_sendAsync", "BE Name cannot be null");
                //         throw("BE Name cannot be null");
                //     }
                //     break;
                case RequestType.RQST:
                    if (!entityName) {
                        await Logger.logError("SyncEngine", "_sendAsync", "BE Name cannot be null");
                        throw("BE Name cannot be null");
                    }

                    if (Object.keys(dataObject).length === 0 || !dataObject[FieldConstants.FieldLid] || !dataObject[FieldConstants.FieldLid].toString().length) {
                        await Logger.logError("SyncEngine", "_sendAsync", `BE LID cannot be null. BE name: ${entityName}`);
                        throw(`BE LID cannot be null. BE name: ${entityName}`);
                    }
                    break;
                default:
                    break;
            }

            const outObjectData = {
                lid: FrameworkHelper.getUUID(),
                timestamp: Date.now(),
                objectStatus: ObjectStatus.global,
                syncStatus: SyncStatus.none,
                functionName: paName,
                beName: entityName,
                beHeaderLid: dataObject[FieldConstants.FieldLid] || "",
                requestType: requestType,
                syncType: isAsyncCall ? SyncType.ASYNC : SyncType.SYNC,
                conversationId: "",
                messageJson: inputDataString,
                companyNameSpace: "",
                sendStatus: "",
                fieldOutObjectStatus: OutObjectStatus.none.toString(),
                isAdminServices: false
            };
            await this.checkInOutBoxAndQueue(outObjectData);
            syncResult.type = ResultType.success;
            syncResult.code = 201;
        } catch (e) {
            syncResult.type = ResultType.error;
            syncResult.error = e.message;
            syncResult.code = 0;
        }
        return syncResult
    }

    private async handleInfoMessage(responseData: { [key: string]: any }, lid: string, entityName: string): Promise<void> {
        if (Object.keys(responseData).length > 0 && responseData[ServiceConstants.KeyInfoMessage]) {
            const infoMessages = responseData[ServiceConstants.KeyInfoMessage];
            for (const element of infoMessages) {
                const infoMessage = {
                    lid: FrameworkHelper.getUUID(),
                    timestamp: Date.now(),
                    objectStatus: ObjectStatus.global,
                    syncStatus: SyncStatus.none,
                    type: "",
                    subtype: "",
                    category: element[FieldConstants.FieldInfoMessageCategory],
                    message: element[FieldConstants.FieldInfoMessageMessage],
                    bename: entityName,
                    belid: lid,
                    messagedetails: ""
                };
                await DatabaseManager.getInstance().insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessage, true)
            }
        }
    }

    public async checkInOutBoxAndQueue(entry: any): Promise<void> {
        let outObject = await OutBoxHelper.checkIsInOutBox(entry.beHeaderLid);

        if (outObject) {
            if (outObject.fieldOutObjectStatus === OutObjectStatus.lockedForSending.toString()) {
                await Logger.logError("SyncEngine", "_checkInOutBoxAndQueue", "Cannot modify. OutObject is locked for sending");
                return;
            }

            if (entry.functionName !== outObject.functionName) {
                await Logger.logError("SyncEngine", "_checkInOutBoxAndQueue", `Cannot modify. OutObject is already queued for ${outObject.functionName}`);
                return;
            }
            outObject["fieldOutObjectStatus"] = OutObjectStatus.lockedForModify.toString();

            const databaseManager = DatabaseManager.getInstance();
            try {
                await Logger.logDebug("SyncEngine", "_checkInOutBoxAndQueue", `Updating to outbox ${outObject.lid}`);
                await databaseManager.update(DatabaseType.FrameworkDb, "OutObject", outObject, `${FieldConstants.FieldLid} = '${outObject.lid}'`);
            } catch (e) {
                await Logger.logError("SyncEngine", "_checkInOutBoxAndQueue", `Update Failed, DBException: ${JSON.stringify(e)}`);
            }
            OutboxService.getInstance().start();
            return;
        } else {
            outObject = entry;
        }

        const sentItem = await OutBoxHelper.checkIsInSentItems(outObject.beHeaderLid);
        if (sentItem) {
            await Logger.logDebug("SyncEngine", "_checkInOutBoxAndQueue", `Sent to outbox ${outObject.lid}`);

            const error = `Object already present in sent items. Cannot queue the same object twice. BE Name: ${outObject.beName} Process Agent Name: ${outObject.functionName} BE Name: ${outObject.beName} BE Lid: ${outObject.beHeaderLid} Data: ${outObject.messageJson}`;
            await Logger.logError("SyncEngine", "_checkInOutBoxAndQueue", error);
            throw(error);
        }

        await OutBoxHelper.updateSyncStatusToEntityObjects(outObject, SyncStatus.queued);
        try {
            await Logger.logDebug("SyncEngine", "_checkInOutBoxAndQueue", `Adding to outbox ${outObject.lid}`);
            const databaseManager = DatabaseManager.getInstance();
            await databaseManager.insert(DatabaseType.FrameworkDb, "OutObject", outObject, true);
            const outObjectList = await databaseManager.select(DatabaseType.FrameworkDb, "OutObject");
            await Logger.logDebug("SyncEngine", "_checkInOutBoxAndQueue", `Checking in outbox ${outObjectList.length}`);
        } catch (e) {
            await Logger.logError("SyncEngine", "_checkInOutBoxAndQueue", `Adding to outbox failed ${outObject.lid} ${JSON.stringify(e)}`);
            const error = `Exception while adding data structure to outbox. BE Name: ${outObject.beName} Process Agent Name: ${outObject.functionName} Exception: ${JSON.stringify(e)} Data: ${outObject.messageJson}`;
            await Logger.logError("SyncEngine", "_checkInOutBoxAndQueue", error);
        }
        await Logger.logInfo("SyncEngine", "checkInOutBoxAndQueue", "Starting Outbox Service");
        OutboxService.getInstance().start();
    }
}

export default new SyncEngine()