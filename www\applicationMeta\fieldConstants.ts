export const FieldLid: string = "LID";
export const FieldFid: string = "FID";
export const FieldConflict: string = "HAS_CONFLICT";
export const FieldTimestamp: string = "TIMESTAMP";
export const FieldSyncStatus: string = "SYNC_STATUS";
export const FieldObjectStatus: string = "OBJECT_STATUS";
export const FieldTableName: string = "TABLE_NAME";
// This field is added to cummulate the highest InfoMessage category
// Priority: FAILUE > FAILURE_N_PROCESS > WARNING > INFO > SUCCESS
export const FieldInfoMsgCat: string = "INFO_MSG_CAT";

export const FieldTypeLid: string = "Text";
export const FieldTypeFid: string = "Text";
export const FieldTypeConflict: string = "Text";
export const FieldTypeTimestamp: string = "Integer";
export const FieldTypeSyncStatus: string = "Integer";
export const FieldTypeObjectStatus: string = "Integer";
export const FieldTypeInfoMsgCat: string = "Text";

export const FieldInfoMessageCategory: string = "category";
export const FieldInfoMessageMessage: string = "message";

export const InfoMessageFailure: string = "FAILURE";
export const InfoMessageFailureAndProcess: string = "FAILURE_N_PROCESS";
export const InfoMessageMsgWithheld: string = "MSG_WITHHELD";
export const InfoMessageWarning: string = "WARNING";
export const InfoMessageInfo: string = "INFO";
export const InfoMessageSuccess: string = "SUCCESS";

export const driftNoElement: string = "No element"; // Returned as an exception message from drift when no row is present in the table

export const InfoMessageError: string = "FAILURE";

// export const yes: string = "YES";

export const messageServiceType: string = "message_service_type";


// Application Metadata Parser Constants

export const ApplicationNameAttribute = "name";
export const ApplicationDescriptionAttribute = "description";
export const ApplicationVersionAttribute = "version";
export const ApplicationNamespaceAttribute = "namespace";
export const ApplicationClassNameAttribute = "applicationClassName";

// Meta Data
export const BeMetaData = "MetaData";
export const BeMetaDataDeleteAttribute = "delete";

// Business Entity
export const BeNode = "BusinessEntity";
export const Be = "BE";

export const BeDescriptionAttribute = "description";
export const BeAttachmentsAttribute = "attachments";
export const BeNameAttribute = "name";
export const BeFieldTextAttribute = "value";
export const BeVersionAttribute = "version";
export const BeAddFunctionAttribute = "addFunction";
export const BeModifyFunctionAttribute = "modifyFunction";
export const BeDeleteFunctionAttribute = "deleteFunction";
export const BeNotificationAttribute = "notification";
export const BeOnConflictAttribute = "onConflict";
export const BeSaveAttribute = "save";

export const BeActionAttribute = "action";

export const ConflictModeServerWins = "SERVER_WINS";
export const ConflictModeDeviceWins = "DEVICE_WINS";
export const ConflictModeAppHandled = "APP_HANDLED";

export const BeItemIndex = "index";
export const BeItemDescriptionAttribute = "description";
export const BeItemNameAttribute = "name";
export const BeItemClassNameAttribute = "className";
export const BeItemActionAttribute = "action";
export const BeItemIsHeader = "header";
export const BeItemIsAttachment = "attachment";

export const BeJsonFieldNode = "field";
export const BeJsonFieldNameNode = "name";
export const BeJsonFieldIsGid = "isGid";
export const BeJsonFieldLength = "length";
export const BeJsonFieldMandatory = "mandatory";
export const BeJsonFieldSqlType = "sqlType";
export const BeJsonFieldDescription = "description";

export const IndexNode = "Index";
export const IndexName = "name";
export const IndexFields = "Fields";
export const IndexTableName = "tableName";