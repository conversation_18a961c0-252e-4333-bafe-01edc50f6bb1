import FrameworkHelper from "./frameworkHelper";

export class UnviredAccount {
    private accountId: string;
    private username: string;
    private company: string;
    private serverURL: string;
    private feUserId: string;
    private loginType: string;
    private port: string;
    private domain: string;
    private jwtToken: string;
    private firstName: string;
    private lastName: string;

    constructor(username: string, company: string, serverURL: string, feUserId: string, loginType: string, jwtToken: string) {
        this.accountId = FrameworkHelper.getUUID();
        this.username = username;
        this.company = company;
        this.serverURL = serverURL;
        this.feUserId = feUserId;
        this.loginType = loginType;
        this.port = "";
        this.domain = "";
        this.jwtToken = jwtToken || "";
        this.firstName = "";
        this.lastName = "";
    }

    public getAccountId(): string {
        return this.accountId;
    }

    public getUsername(): string {
        return this.username;
    }

    public getCompany(): string {
        return this.company;
    }

    public getServerURL(): string {
        return this.serverURL;
    }

    public getFeUserId(): string {
        return this.feUserId;
    }

    public getLoginType(): string {
        return this.loginType;
    }

    public getPort(): string {
        return this.port;
    }

    public getDomain(): string {
        return this.domain;
    }

    public getJwtToken(): string {
        return this.jwtToken;
    }

    public getFirstName(): string {
        return this.firstName;
    }

    public getLastName(): string {
        return this.lastName;
    }

    public setUsername(username: string): void {
        this.username = username;
    }

    public setCompany(company: string): void {
        this.company = company;
    }

    public setServerURL(serverURL: string): void {
        this.serverURL = serverURL;
    }

    public setFeUserId(feUserId: string): void {
        this.feUserId = feUserId;
    }

    public setLoginType(loginType: string): void {
        this.loginType = loginType;
    }

    public setPort(port: string): void {
        this.port = port;
    }

    public setDomain(domain: string): void {
        this.domain = domain;
    }

    public setJwtToken(jwtToken: string): void {
        this.jwtToken = jwtToken;
    }

    public setFirstName(firstName: string): void {
        this.firstName = firstName;
    }

    public setLastName(lastName: string): void {
        this.lastName = lastName;
    }

    static fromJSON(json: any): UnviredAccount {
        var acc = new UnviredAccount(json.username, json.company, json.serverURL, json.feUserId, json.loginType, json.jwtToken);
        acc.accountId = json.accountId;
        acc.port = json.port;
        acc.domain = json.domain;
        acc.firstName = json.firstName;
        acc.lastName = json.lastName;
        return acc;
    }

    toJSON(): any {
        return {
            accountId: this.accountId,
            username: this.username,
            company: this.company,
            serverURL: this.serverURL,
            feUserId: this.feUserId,
            loginType: this.loginType,
            port: this.port,
            domain: this.domain,
            jwtToken: this.jwtToken,
            firstName: this.firstName,
            lastName: this.lastName
        };
    }
}