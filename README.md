# cordova-plugin-unvired-universal-sdk
This cordova plugin is published as a [npm package](https://www.npmjs.com/package/cordova-plugin-unvired-universal-sdk)

# Using this plugin
1. Uninstall the existing dependency plugins (if any)
```
$ cordova plugin rm cordova-plugin-device --force
$ cordova plugin rm cordova-plugin-file --force
```

2. Uninstall the previous version (if any) of the plugin
```
$ cordova plugin rm cordova-plugin-unvired-universal-sdk
```

3. Install this plugin
```
$ cordova plugin add cordova-plugin-unvired-universal-sdk
```

4. Verify the installation by checking the version number with package.json file or by listing the Cordova plugins installed for your project
```
$ cordova plugin ls   // List Cordova plugins
```

5. Make sure that the following JS dependencies are updated in index.html file. These JS dependencies should have been copied to the respective locations when you install the plugin.
```
  <script src="assets/js/codemirror.js"></script>
  <script src="assets/js/sql.js"></script>
  <script src="assets/js/jquery-3.2.1.js"></script>
  <script src="assets/js/unvired-db-worker.js"></script>
```

# Building this plugin

This contains source code for Unvired Cordova Plugin. In order to test this plugin locally, generate a debug version by building using build-debug.xml

```
$ ant -buildfile build-debug.xml
```

This produces the output in a folder called cordova-plugin-unvired-universal-sdk.


# Adding a new method.

Any new method should have the implementation in www/kernel.js file. Accordingly, all the native cores should have an implementation for this method.

# Exposing the new method to ionic apps.

This plugin is repackaged as ionic native plugin.
https://ionicframework.com/docs/native/unvired-cordova-sdk

The source code of the ionic native plugin is located here:
https://github.com/ionic-team/ionic-native/tree/master/src/%40ionic-native/plugins/unvired-cordova-sdk

 The source contains a single file, index.ts which contains declarations of all the functions available in www/kernel.js file of this repo. To expose newly added methods in kernel.js, you will have to fork https://github.com/ionic-team/ionic-native repo to your GitHub repo, make changes and submit a pull request.

Once the Pull request is approved, the new method should be available publicly for all the users.