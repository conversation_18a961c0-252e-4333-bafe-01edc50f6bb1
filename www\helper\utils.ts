import { URLService } from "./urlService";

declare var Logger: any;

export enum LoginType {
    unvired = 'UNVIRED_ID',
    ads = 'ADS',
    sap = 'SAP',
    email = 'EMAIL',
    saml2 = 'SAML2',
    custom = 'CUSTOM',
    passwordless = "PASSWORDLESS"
}

export enum ObjectStatus {
  global = 0,
  add = 1,
  modify = 2,
  delete = 3
}

export enum SyncStatus {
  none = 0,
  queued = 1,
  sent = 2,
  error = 3
}

export enum LoginListenerType {
  auth_activation_required = 0, // Mobile app not yet activated and requires authentication and activation
  app_requires_login = 1, // Mobile app requires offline / local login
  auth_activation_success = 2, // Account authenticated and activated on the server
  auth_activation_error = 3, // Acoount authentication or aactivation failed on the server
  login_success = 4, // Mobile app login successful
  login_error = 5, // Mobile app login failure
  app_requires_current_account = 6, // Multiple account found hence app has to set current active account
  login_demo = 7 // This value indicates app can proceed with demo mode.
};

export enum LogType {
  debug,
  info,
  error
}

export class LoginParameters {
    appName: string;
    company: string;
    username: string;
    password: string;
    url: string;
    domain: string;
    loginType: LoginType;
    feUserId: string;
    port: string;
    isRequiredAttachmentBase64: boolean;
    autoSendTime: string;
    autoSyncTime: string;
    metadataJSON: string;
    demoData: string;
    demoModeRequired: boolean;
    persistWebDb: boolean;
    jwtOptions: object;
    loginLanguage: string;
    cacheWebData: boolean;
    requireClientCredentials: boolean;
}

export async function isInternetConnected(): Promise<boolean> {
    let internetConnected = false
    try {
      const response = await fetch('https://www.google.com/favicon.ico'); // Replace with a reliable resource
      if (response.ok) {
        Logger.logInfo("Utils", "isInternetConnected", "You are connected to the internet");
        internetConnected = true
      } else {
        Logger.logInfo("Utils", "isInternetConnected", "There might be internet connection issues");
      }
    } catch (error) {
      Logger.logError("Utils", "isInternetConnected", "Error checking internet connection: " + error);
    }
    return internetConnected
}

export async function isServerReachable(baseUrl: string): Promise<boolean> {
  return true;
  if (baseUrl == null || baseUrl.length == 0) {
      await Logger.logError("Utils", "isServerReachable", "Server URL is empty.");
      return false;
  }
  const pingUrl = URLService.getPingUrl(baseUrl);
  const url = new URL(pingUrl);
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 seconds timeout
  try {
      const response = await fetch(url.toString(), {
          headers: {
              "Accept": "application/json"
          },
          signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response.status === 200;
  } catch (error) {
      if (error.name === 'AbortError') {
          await Logger.logError("HTTPConnection", "isServerReachable", "Server not reachable within 10 seconds. Error: " + error);
      } else {
          await Logger.logError("HTTPConnection", "isServerReachable", "Error while pinging to server. Error: " + error);
      }
  }
  return false;
}