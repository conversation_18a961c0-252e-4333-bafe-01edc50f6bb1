import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import FrameworkHelper from "../helper/frameworkHelper";
import ServerResponseHandler from "../helper/serverResponseHandler";
import { ObjectStatus, SyncStatus } from "../helper/utils";
import DownloadMessageService from "./downloadMessageService";
import * as ServiceConstants from "../helper/serviceConstants";
import * as FieldConstants from "../applicationMeta/fieldConstants";
import InboxHelper from "./inboxHelper";
import AttachmentService from "../attachment/attachmentService";
import { NotificationListenerHelper, NotificationListnerType } from "../helper/notificationListnerHelper";

declare var Logger: any;

const fileName = "InboxService"
export default class InboxService {
    
    private static instance: InboxService;
    private isProcessing: boolean = false;
    private shouldStop: boolean = false;

    private constructor() {}

    public static getInstance(): InboxService {
        if (!InboxService.instance) {
            InboxService.instance = new InboxService();
        }
        return InboxService.instance;
    }

    public start(): void {
        if (!this.isProcessing) {
            this.isProcessing = true;
            this.shouldStop = false;
            this.startInboxProcessing();
        } else {
            Logger.logInfo(fileName, "start", "Inbox processing is already running.");
        }
    }

    public stop(): void {
        if (this.isProcessing) {
            this.shouldStop = true;
            Logger.logInfo(fileName, "stop", "Stopping inbox processing...");
        } else {
            Logger.logInfo(fileName, "stop", "Inbox processing is not running.");
        }
    }

    async startInboxProcessing(): Promise<void> {
        Logger.logInfo(fileName, "startInboxProcessing", "Starting inbox processing...");
        let isInboxEmpty = false;
        const databaseManager = DatabaseManager.getInstance();
        const inboxCount = await databaseManager.count(DatabaseType.FrameworkDb, "InObject");
        var currentIndex = 0;
        try {
            do {
                if (this.shouldStop) {
                    Logger.logInfo(fileName, "startInboxProcessing", "Stopping inbox processing...");
                    break;
                }
                const inObject = await databaseManager.getFirstEntity(DatabaseType.FrameworkDb, "InObject");
                isInboxEmpty = inObject === null;
                if (!isInboxEmpty) {
                    currentIndex++;
                    const currentProcessingNumber = inboxCount - currentIndex;
                    if (currentProcessingNumber > 0) {
                        NotificationListenerHelper.postSynchronizationStateNotification(`processing (${currentProcessingNumber})`);
                    }
                    Logger.logInfo(fileName, "startInboxProcessing", `Processing LID : ${inObject.lid}. Conversation ID : ${inObject.conversationId}.`);
                    try {
                        if (inObject.type !== -1 && inObject.subtype !== -1) {
                            Logger.logInfo(fileName, "startInboxProcessing", `Admin service messages. Starting in-object processing.`);
                            await DownloadMessageService.handleMessageTypes(inObject);
                            await databaseManager.delete(DatabaseType.FrameworkDb, "InObject", `conversationId = '${inObject.conversationId}'`);
                            Logger.logInfo(fileName, "startInboxProcessing", `Admin service messages. Deleting from in-object.`);
                            await InboxHelper.deleteInBoxData(inObject.conversationId);
                        } else {
                            let jsonData: any = {};
                            try {
                                const inboxData = await InboxHelper.getInBoxData(inObject.conversationId);
                                jsonData = JSON.parse(inboxData);
                            } catch (e) {
                                Logger.logError(fileName, "startInboxProcessing", `Application messages. Error while getting inbox data: ${JSON.stringify(e)}.`);
                                await databaseManager.delete(DatabaseType.FrameworkDb, "InObject", `lid = '${inObject.lid}'`);
                                continue;
                            }
                            
                            try {
                                Logger.logInfo(fileName, "startInboxProcessing", `Application messages. Starting in-object processing.`);
                                // map["currentConvId"] = inObject.conversationId;
                                await ServerResponseHandler.handleResponseData(jsonData, true, false, inObject.beLid, inObject.jsonData, inObject.requestType);
                                Logger.logInfo(fileName, "startInboxProcessing", `Application messages. In-object successfully processed.`);
                            } catch (e) {
                                Logger.logError(fileName, "startInboxProcessing", `Application messages. Error while processing in-object: ${JSON.stringify(e)}.`);
                                const infoMessage = {
                                    lid: FrameworkHelper.getUUID(),
                                    timestamp: Date.now(),
                                    objectStatus: ObjectStatus.global,
                                    syncStatus: SyncStatus.none,
                                    type: inObject.type.toString(),
                                    subtype: inObject.subtype.toString(),
                                    category: FieldConstants.InfoMessageFailure,
                                    message: `Deleted the InObject from the inobject Queue. Could not handle the In Object because the XML data had a parsing Issue: ${JSON.stringify(e)}. Conversation ID: ${inObject.conversationId}. `,
                                    bename: inObject.jsonData,
                                    belid: inObject.beLid,
                                    messagedetails: ""
                                };
                                await databaseManager.insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessage, true);
                                await this.updateSyncStatusToEntityObjects(inObject, SyncStatus.error);
                            }
                            Logger.logInfo(fileName, "startInboxProcessing", `Application messages. Deleting from in-object.`);
                            const result: number = await databaseManager.delete(DatabaseType.FrameworkDb, "InObject", `conversationId = '${inObject.conversationId}'`);
                            Logger.logInfo(fileName, "startInboxProcessing", `Application messages. in-object deleted. Result: ${result}`);
                            await InboxHelper.deleteInBoxData(inObject.conversationId);
                        }
                    } catch (e) {
                        Logger.logError(fileName, "startInboxProcessing", `Application messages. Error while processing in-object: ${JSON.stringify(e)}.`);
                    }
                } else {
                    Logger.logInfo(fileName, "startInboxProcessing", `Inbox is empty. Stopping inbox processing.`);
                }
            } while (!isInboxEmpty);
            await this.checkAndUpdateHeaderSyncStatus();
            Logger.logInfo(fileName, "startInboxProcessing", `Inbox processing completed.`);
            NotificationListenerHelper.postSynchronizationStateNotification(`idle`);
            if (inboxCount > 0) {
                NotificationListenerHelper.postDataSenderNotification(null, NotificationListnerType.incomingDataProcessingFinished);
            }
            AttachmentService.getInstance().start();
        }
        catch (e) {
            Logger.logError(fileName, "startInboxProcessing", `Error while starting inbox processing. Error: ${e}`);
        }
        finally {
            this.isProcessing = false;
            this.shouldStop = false;
        }
    }
    
    private async checkAndUpdateHeaderSyncStatus(): Promise<void> {
        try {
            const databaseManager = DatabaseManager.getInstance();
            const headerStructMetas: any[] = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `isHeader = '1'`);
            const allBusinessEntityMetas: any[] = await databaseManager.select(DatabaseType.FrameworkDb, "BusinessEntityMeta");
            const allSentItems = await databaseManager.select(DatabaseType.FrameworkDb, "SentItems");
            const allBeLids: string[] = allSentItems.map(e => e.beHeaderLid);
            let beLidsSting = "";
            if (allBeLids.length > 0) {
                beLidsSting = "'" + allBeLids.join("','") + "'";
            }
            for (const headerStructure of headerStructMetas) {
                try {
                    const currentBeMeta = allBusinessEntityMetas.find(element => element.beName === headerStructure.beName);
                    if (currentBeMeta?.save === "false") {
                        continue;
                    }
                    Logger.logInfo(fileName, "checkAndUpdateHeaderSyncStatus", `headerStructure: ${JSON.stringify(headerStructure)}.`);
                    const allData: any[] = await databaseManager.select(DatabaseType.AppDb, headerStructure.structureName, `${FieldConstants.FieldSyncStatus}=${SyncStatus.sent} AND ${FieldConstants.FieldObjectStatus}=${ObjectStatus.modify}` +
                        (beLidsSting.length > 0 ? ` AND ${FieldConstants.FieldLid} NOT IN (${beLidsSting})` : ""))
                    for (const headerData of allData) {
                        try {
                            await databaseManager.update(DatabaseType.AppDb, headerStructure.structureName, {
                                [FieldConstants.FieldSyncStatus]: SyncStatus.error,
                                [FieldConstants.FieldInfoMsgCat]: FieldConstants.InfoMessageFailure
                            }, `${FieldConstants.FieldLid} = '${headerData[FieldConstants.FieldLid]}'`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null.
                            const infoMessage = {
                                lid: FrameworkHelper.getUUID(),
                                timestamp: Date.now(),
                                objectStatus: ObjectStatus.global,
                                syncStatus: SyncStatus.none,
                                type: "",
                                subtype: "",
                                category: FieldConstants.InfoMessageFailure,
                                message: "The connection was interrupted. Please try again.",
                                bename: headerStructure.structureName,
                                belid: headerData[FieldConstants.FieldLid],
                                messagedetails: ""
                            };
                            await databaseManager.insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessage, true);
                        } catch (e) {
                            Logger.logError(fileName, "checkAndUpdateHeaderSyncStatus", `Error while updating header (${headerStructure.structureName}) Sync Status to ERROR into database. Error: ${JSON.stringify(e)}`);
                        }
                    }
                } catch (e) {
                    Logger.logError(fileName, "checkAndUpdateHeaderSyncStatus", `Error while getting BE Meta for header (${headerStructure.structureName}). Error: ${JSON.stringify(e)}`);
                }
            }
        } catch (e) {
            Logger.logError(fileName, "checkAndUpdateHeaderSyncStatus", `Error while updating header Sync Status to ERROR. Error: ${JSON.stringify(e)}`);
        }
    }
    
    async updateSyncStatusToEntityObjects(inObject: any, syncStatus: SyncStatus): Promise<void> {
        const requestType = inObject.requestType;
        const entityName = inObject.jsonData; // BE Name is set in the jsonData field
        const beLid = inObject.beLid;
        
        if (requestType === ServiceConstants.RequestType.RQST.toString()) {
            const databaseManager = DatabaseManager.getInstance();
            const structureMetaData = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `structureName = '${entityName}'`)
            if (structureMetaData.length === 0) {
                Logger.logError(fileName, "updateSyncStatusToEntityObjects", `No structure meta found for BE-NAME: ${entityName}, BE-LID: ${beLid}`);
                return;
            }
            const structName = structureMetaData[0].structureName;
            const structureFieldData = await databaseManager.select(DatabaseType.AppDb, structName, `${FieldConstants.FieldLid} = '${beLid}'`);
            if (structureFieldData.length === 0) {
                Logger.logError(fileName, "updateSyncStatusToEntityObjects", `No Business Entity got from database, BE-NAME: ${entityName}, BE-LID: ${beLid}`);
                return;
            }
            const data = structureFieldData[0];
            
            data[FieldConstants.FieldSyncStatus] = syncStatus;
            await databaseManager.update(DatabaseType.AppDb, structName, data, `${FieldConstants.FieldLid} = '${beLid}'`);
                
            const childStructureMetas = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `beName = '${entityName}' AND isHeader != '1'`);
            for (const childStructureMetaData of childStructureMetas) {
                await databaseManager.update(DatabaseType.AppDb, childStructureMetaData.structureName, {
                    [FieldConstants.FieldSyncStatus]: syncStatus
                }, `${FieldConstants.FieldLid}='${beLid}' AND ${FieldConstants.FieldObjectStatus} != ${ObjectStatus.global}`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null.    
            }
        }
    }
}