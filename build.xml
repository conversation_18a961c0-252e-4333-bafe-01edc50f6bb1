<project name="cordova-plugin-unvired-universal-sdk" default="npmpublish" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">
	<property name="dist.dir" value="${basedir}/dist"/>
	<property name="build.dir" value="${basedir}/build/cordova-plugin-unvired-universal-sdk"/>

	 <scriptdef name="substring" language="javascript">
	     <attribute name="text" />
	     <attribute name="start" />
	     <attribute name="end" />
	     <attribute name="property" />
	     <![CDATA[
	       var text = attributes.get("text");
	       var start = attributes.get("start");
	       var end = attributes.get("end") || text.length();
	       project.setProperty(attributes.get("property"), text.substring(start, end));
	     ]]>
  	</scriptdef>

	<scriptdef name="packageversion" language="javascript">
	    <attribute name="text" />
	    <attribute name="start" />
	    <attribute name="end" />
	    <attribute name="property" />
	    <![CDATA[
			var text = attributes.get("text");
	       	var start = attributes.get("start");
	       	var end = attributes.get("end") || text.length();
	       	var newstring = text.substring(start, end);
			newstring = newstring.replace(/\./g,'~');
	       	var split = newstring.split("~");
	       	var first = +split[0];
	       	first = first.toString();
	       	var middle = +split[1];
			middle = middle.toString();
	       	var last = +split[2];
			last = last.toString();
	       	
	       	newstring = first + '.' + middle + '.' + last;
	       	project.setProperty(attributes.get("property"), newstring);
	    ]]>
  	</scriptdef>  

    <!-- Get the release number -->
    <target name="getbuildno">
        <property environment="env" />

        <java jar="/Users/<USER>/Jenkins/BuildNo/BuildNo.jar" fork="true" failonerror="true" maxmemory="128m">
            <arg value="CORDOVA_PLUGIN_UNVIRED_UNIVERSAL_SDK"/>
            <arg value="-r=true"/>
            <arg value="-n=true"/>
        </java>

        <!-- Now read into the build numberfile into release.str property -->
        <loadfile property="release.str"
            srcFile="BuildNo.txt" failonerror="true">
        </loadfile>

        <echo message="Using release number : ${release.str}"/>
		
    </target>

	<target name="npminstall" depends="getbuildno">
		<echo message="Doing npm install"/>
		<exec executable="npm"  failonerror="true">
			<arg value="install"/>
		</exec>
    </target>

	<target name="updatebuildno" depends="npminstall">
		<echo message="Updating build number in service constants file."/>
		
		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>
		<packageversion text="${release.str}" start="2" property="release.num" />

		<replace file="www/helper/serviceConstants.ts" token="1.1.1" value='${release.num}'/>
		<replace file="www/helper/serviceConstants.ts" token="@BUILD_NUMBER@" value='${BUILD_NUMBER}'/>
    </target>

	<target name="npmbuild" depends="updatebuildno">
		<echo message="Run Build"/>
		<exec executable="npm"  failonerror="true">
			<arg value="run"/>
			<arg value="build"/>
		</exec>
    </target>

	<target name="package" depends="npmbuild">
		<property environment="env" />

		<mkdir dir="${dist.dir}"/>
		<mkdir dir="cordova-plugin-unvired-universal-sdk"/>

		<copy overwrite="true" todir="cordova-plugin-unvired-universal-sdk/www">
			<fileset dir="${build.dir}/www"/>
		</copy>
		
		<copy file="package.json" tofile="cordova-plugin-unvired-universal-sdk/package.json" overwrite="true"/>
		<copy file="plugin.xml" tofile="cordova-plugin-unvired-universal-sdk/plugin.xml" overwrite="true"/>
		<copy file="README.md" tofile="cordova-plugin-unvired-universal-sdk/README.md" overwrite="true"/>
    </target>	

	<target name="updatesource" depends="package">

		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<property environment="env" />

		<packageversion text="${release.str}" start="2" property="release.num" />
		<echo message="Using package version number : ${release.num}"/>
		<replace file="cordova-plugin-unvired-universal-sdk/package.json" token="1.1.1" value='${release.num}'/>
		<replace file="cordova-plugin-unvired-universal-sdk/plugin.xml" token="1.1.1" value='${release.num}'/>
		<tar destfile="dist/cordova-plugin-unvired-universal-sdk.tgz" basedir="cordova-plugin-unvired-universal-sdk" longfile="posix" compression="gzip" />
    </target>   
	
    <target name="npmpublish" depends="updatesource" if="${env.PUBLISH}">
		<echo message="Publishing to NPM Registry"/>
		<exec executable="npm"  failonerror="true">
			<arg value="publish"/>
			<arg value="./cordova-plugin-unvired-universal-sdk"/>
		</exec>
    </target>
</project>