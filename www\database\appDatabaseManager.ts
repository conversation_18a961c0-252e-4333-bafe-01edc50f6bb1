import DatabaseManager, { DatabaseType } from "./databaseManager";

export class AppDatabaseManager {

    private static instance: AppDatabaseManager | null = null;
    private constructor() {
    }
    public static getInstance(): AppDatabaseManager {
        if (!AppDatabaseManager.instance) {
            AppDatabaseManager.instance = new AppDatabaseManager();
        }
        return AppDatabaseManager.instance;
    }

    public async select(tableName: string, whereClause: string = ""): Promise<any> {
        return await DatabaseManager.getInstance().select(DatabaseType.AppDb, tableName, whereClause)
    }

    public async insert(tableName: string, structureObject: any, isHeader: boolean): Promise<any> {
        return await DatabaseManager.getInstance().insert(DatabaseType.AppDb, tableName, structureObject, isHeader, true)
    }
  
    public async insertOrUpdate(tableName: string, structureObject: any, isHeader: boolean): Promise<any> {
        return await DatabaseManager.getInstance().insertOrUpdate(DatabaseType.AppDb, tableName, structureObject, isHeader, true)
    }
    
    public async delete(tableName: string, whereClause: string = ""): Promise<any> {
        return await DatabaseManager.getInstance().delete(DatabaseType.AppDb, tableName, whereClause)
    }
    
    public async update(tableName: string, updatedObject: any, whereClause: any): Promise<any> {
        return await DatabaseManager.getInstance().update(DatabaseType.AppDb, tableName, updatedObject, whereClause, true)
    }
    
    public async executeStatement(query: string): Promise<any> {
        return await DatabaseManager.getInstance().executeStatement(DatabaseType.AppDb, query)
    }

    public async createSavePoint(savePoint: string): Promise<void> {
        return await DatabaseManager.getInstance().createSavePoint(DatabaseType.AppDb, savePoint)
    }

    public async releaseSavePoint(savePoint: string): Promise<void> {
        return await DatabaseManager.getInstance().releaseSavePoint(DatabaseType.AppDb, savePoint)
    }
    
    public async rollbackToSavePoint(savePoint: string): Promise<void> {
        return await DatabaseManager.getInstance().rollbackToSavePoint(DatabaseType.AppDb, savePoint)
    }
    
    public async beginTransaction(): Promise<void> {
        return await DatabaseManager.getInstance().beginTransaction(DatabaseType.AppDb)
    }
    
    
    public async endTransaction(): Promise<void> {
        return await DatabaseManager.getInstance().endTransaction(DatabaseType.AppDb)
    }
        
    public async rollbackTransaction(): Promise<void> {
        return await DatabaseManager.getInstance().rollbackTransaction(DatabaseType.AppDb)
    }
}

export default AppDatabaseManager