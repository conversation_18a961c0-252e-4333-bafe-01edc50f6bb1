import { v4 as uuidv4 }  from 'uuid';
// import { electron } from 'webpack';
import * as crypto from "crypto-js";

declare var Logger: any;
declare global {
  interface Window {
    cordova: any; // This is defined from the cordova plugin
    device: any; // This is defined from the cordova-plugin-device plugin
    resolveLocalFileSystemURL: any;
    UnviredDB: any; // This is defined from the cordova-plugin-unvired-db
    FirebasePlugin: any; // This is defined from the cordova-plugin-firebasex plugin
  }
}

export default class FrameworkHelper {
    static getPlatform(): string {
      const platform = window.cordova.platformId//window.device.platform;
      Logger.logInfo("FrameworkHelper", "getPlatform", "Platform: " + platform);
      return platform.toLowerCase(); //"electron"
    }
    
    static getUUID(): string {
        return uuidv4().replace(/-/g, '');
    }

    static getFrontendType() {
      const platform = window.cordova.platformId;
      const deviceModel = window.device.model;
      Logger.logInfo("FrameworkHelper", "getFrontendType", "Platform: " + platform + " Device Model: " + deviceModel);
      
      switch (platform) {
        case "ios":
          if (/iphone|ipod/i.test(deviceModel)) {
            return 'IPHONE';
          }
          else if (/ipad/i.test(deviceModel)) {
            return 'IPAD';
          }
        case "android":
          if (/tablet/i.test(deviceModel)) {
            return 'ANDROID_TABLET';
          } else {
            return 'ANDROID_PHONE';
          }
        case "browser":
          return "BROWSER";
        case "electron":
          return "WINDOWS";
        default:
          return "IPHONE";
      }
    }

    static getMD5String(str) {
      if (str == null || str == undefined || str == "") {
        return "";
      }
      const bytes = new TextEncoder().encode(str);
      try {
        var md5String = crypto.MD5(str).toString();
        Logger.logInfo("FrameworkHelper", "getMD5String", `MD5 String: ${md5String}`);
        return md5String;
      }
      catch (e) {
        Logger.logError("FrameworkHelper", "getMD5String", `Error: ${JSON.stringify(e)}`);
        return "";
      }
    }

    static getDocumentDirectory() {
      return window.cordova.file.dataDirectory;
    }

    static async getFolderBasedOnUserId(userId: string): Promise<string> {
      return new Promise((resolve, reject) => {
        const userPath = this.getDocumentDirectory() + userId
        window.resolveLocalFileSystemURL(userPath, (dir) => {
          Logger.logInfo("FrameworkHelper", "getFolderBasedOnUserId", "Directory exists: " + dir.fullPath);
          resolve(dir.nativeURL);
        }, (error) => {
          Logger.logInfo("FrameworkHelper", "getFolderBasedOnUserId", "Directory did not exist. Error: " + error);
          // Directory did not exist, so creating it.
          Logger.logInfo("FrameworkHelper", "getFolderBasedOnUserId", "Directory did not exist, so creating it: " + userPath);
          window.resolveLocalFileSystemURL(this.getDocumentDirectory(), (parentDir) => {
            parentDir.getDirectory(userId, {create: true}, (newDir) => {
              Logger.logInfo("FrameworkHelper", "getFolderBasedOnUserId", "Directory created: " + newDir.nativeURL);
              resolve(newDir.nativeURL);
            }, (error) => {
              Logger.logError("FrameworkHelper", "getFolderBasedOnUserId", "Unable to create folder for userId: " + userId + ". Error: " + error);
              resolve("");
            });
          }, (error) => {
            Logger.logError("FrameworkHelper", "getFolderBasedOnUserId", "Unable to get parent directory for userId: " + userId + ". Error: " + error);
            resolve("");
          })
        })
      })
    } 

    static deleteUserFolder(userId: string): Promise<void> {
      return new Promise(async (resolve, reject) => {
        const userPath = this.getDocumentDirectory() + userId;
        window.resolveLocalFileSystemURL(userPath, (dir) => {
          Logger.logInfo("FrameworkHelper", "deleteUserFolder", "Directory exists: " + dir.fullPath);
          // Try removeRecursively first
          dir.removeRecursively(() => {
            Logger.logInfo("FrameworkHelper", "deleteUserFolder", "User folder removed");
            resolve();
          }, async (error) => {
            // Fallback: manual recursive deletion
            try {
              await FrameworkHelper.deleteDirectoryContents(dir);
              dir.remove(() => {
                Logger.logInfo("FrameworkHelper", "deleteUserFolder", "User folder removed after manual cleanup");
                resolve();
              }, (removeError) => {
                Logger.logError("FrameworkHelper", "deleteUserFolder", "Remove user folder error after manual cleanup: " + JSON.stringify(removeError));
                reject(removeError);
              });
            } catch (cleanupError) {
              Logger.logError("FrameworkHelper", "deleteUserFolder", "Manual cleanup failed: " + JSON.stringify(cleanupError));
              reject(cleanupError);
            }
          });
        },
        (error) => {
          Logger.logError("FrameworkHelper", "deleteUserFolder", "Get user folder error: " + error);
          reject(error);
        });
      });
    }

    static deleteDirectoryContents(dirEntry): Promise<void> {
      Logger.logInfo("FrameworkHelper", "deleteDirectoryContents", "DIRECTORY FULL PATH: " + dirEntry.fullPath)
      return new Promise((resolve, reject) => {
        const reader = dirEntry.createReader();
        reader.readEntries(async (entries) => {
          try {
            for (const entry of entries) {
              if (entry.isDirectory) {
                await FrameworkHelper.deleteDirectoryContents(entry);
                await new Promise((res, rej) => entry.remove(res, rej));
              } else {
                await new Promise((res, rej) => entry.remove(res, rej));
              }
            }
            resolve();
          } catch (e) {
            reject(e);
          }
        }, reject);
      });
    }
}