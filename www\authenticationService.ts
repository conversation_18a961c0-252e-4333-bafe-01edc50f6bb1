import {LoginParameters, LoginType, LoginListenerType, SyncStatus, ObjectStatus} from './helper/utils'
import FrameworkHelper from "./helper/frameworkHelper";
import HttpConnection from './helper/httpConnection'
import ApplicationMetaParser from './applicationMeta/applicationMetadataParser';
import  * as ServiceConstants  from './helper/serviceConstants';
import { UserSettingsFields, UserSettingsManager, stringToUserSettingsFields } from './helper/userSettingsManager';
import { UnviredAccountManager } from './helper/unviredAccountManager';
import DbCreateTablesManager from './helper/dbCreateTablesManager';
import { UnviredAccount } from './helper/unviredAccount';
import { FrameworkSettingsFields, FrameworkSettingsManager, stringToFrameworkSettingsFields } from './helper/frameworkSettingsManager';
import { Status } from './helper/status';
import SettingsHelper from './helper/settingsHelper';
import { NotificationListenerHelper, NotificationListnerType } from './helper/notificationListnerHelper';
import GetMessageTimerManager from './helper/getMessageTimerManager';
import DatabaseManager, { DatabaseType } from './database/databaseManager';
import OutboxService from './outbox/outboxService';
import SsePushService from './helper/ssePushService';
import { FirebasePushService } from './firebasePushService';

declare var Logger: any;

const fileName = "AuthenticationService"
export class UnviredResult {
    data: any;
    message: string;
    type: number;
    error: string;
    errorDetail: string;
}

export enum ResultType {
    success = 0,
    error,
}

export class SyncResult extends UnviredResult {
    code: number;
}

export class AuthenticationService {
    jwtToken = ""
    public loginParameters: any = {};
    private messageInterval: number = 0;

    private static readonly _instance: AuthenticationService = new AuthenticationService();
    private constructor() {}
    public static get instance(): AuthenticationService {
        return this._instance;
    }

    public async login(loginParameters: LoginParameters): Promise<any> {
        return new Promise(async (resolve, reject) => {
            let cbResult = new UnviredResult()
            try {
                if (loginParameters == null) {
                    cbResult.message = "Invalid input. Login Parameter is null";
                    cbResult.type = LoginListenerType.login_error;
                    resolve(cbResult);
                    return;
                } 
                this.loginParameters = loginParameters
                Logger.logInfo(fileName, "login", "Started Logging in...");

                if (this.loginParameters.metadataPath != null && this.loginParameters.metadataPath.length > 0) {
                    const metadataJSON = await HttpConnection.getJSONFromFile(this.loginParameters.metadataPath);
                    this.loginParameters.metadataJSON = metadataJSON;
                }

                if (this.loginParameters.metadataJSON == null || this.loginParameters.metadataJSON.length == 0) {
                    Logger.logError(fileName, "login", "Metadata is not set.");
                    cbResult.message = "Metadata json is not set.";
                    cbResult.type = LoginListenerType.auth_activation_required;
                    resolve(cbResult);
                    return;
                }

                try {
                    const metadataJSON = JSON.parse(this.loginParameters.metadataJSON)
                    if (metadataJSON == null || metadataJSON == undefined || metadataJSON.length === 0) {
                        Logger.logError(fileName, "login", "Invalid metadata");
                        cbResult.message = "Invalid metadata json is set.";
                        cbResult.type = LoginListenerType.auth_activation_required;
                        resolve(cbResult);
                        return;
                    }
                } catch (e) {
                    Logger.logError(fileName, "login", "Invalid metadata");
                    cbResult.message = "Invalid metadata json is set.";
                    cbResult.type = LoginListenerType.auth_activation_required;
                    resolve(cbResult);
                    return;
                }

                if (this.loginParameters.appName == null || this.loginParameters.appName.length == 0) {
                    ApplicationMetaParser.instance.parseAppInfo(this.loginParameters.metadataJSON);
                    if (ApplicationMetaParser.instance.applicationName.length === 0) {
                        Logger.logError(fileName, "login", "Invalid metadata");
                        cbResult.message = "Application name not initialized";
                        cbResult.type = LoginListenerType.auth_activation_required;
                        resolve(cbResult);
                        return
                    }
                    this.loginParameters.appName = ApplicationMetaParser.instance.applicationName
                    Logger.logInfo(fileName, "login", `APPLICATION NAME: ${this.loginParameters.appName} DEVICE TYPE:${FrameworkHelper.getPlatform()}`);
                }
                
                // Check if DB available
                let allAccounts = UnviredAccountManager.getInstance().getAllAccounts();
                let isDBAvailable = allAccounts.length > 0

                Logger.logInfo(fileName, "login", `Database Status: ${isDBAvailable ? "Available" : "Not Available"}`);

                // If Database is not available, then give a callback to open the Login Screen.
                if (!isDBAvailable) {
                    Logger.logError(fileName, "login", `No Accounts Are Available.`);
                    cbResult.type = LoginListenerType.auth_activation_required;
                    resolve(cbResult);
                    return
                }

                Logger.logInfo(fileName, "login", `All Accounts: ${JSON.stringify(allAccounts)}`);

                

                // DB is available, there is data.
                // If an Account is Logged out, then give a callback to open the Account Selection Screen.
                // Or if there is more than one account, then present Account Selection Screen
                // Local Storeage stores the last logged out account. Fetch it from there.

                let lastLoggedOutAccount = UnviredAccountManager.getInstance().getlastLoggedOutAccount()
                let lastActiveAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount()
                if (lastLoggedOutAccount != null && allAccounts.length > 1) {     // There was an account which was logged out. So New Account has to get selected.
                    cbResult.type = LoginListenerType.app_requires_current_account;
                    resolve(cbResult);
                    return
                }
                else {
                    if (lastActiveAccount == null) {
                        lastActiveAccount = allAccounts[0]
                    }
                }

                const localPasswordFlag = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.localPassword)
                let isLocalPasswordRequired = localPasswordFlag == "YES"

                if (isLocalPasswordRequired) {
                    cbResult.type = LoginListenerType.app_requires_login;
                    resolve(cbResult);
                    return
                }
                else {

                    if(FrameworkHelper.getPlatform() === "browser") {
                        const metadataParser = ApplicationMetaParser.instance;
                        await metadataParser.init(this.loginParameters.metadataJSON);
                        const isDBAvailable = await this.checkIfDataPresentInIndexDB(lastActiveAccount.getAccountId())
                        if (!isDBAvailable) {                      
                            // Create FW Tables
                            const result = await DatabaseManager.getInstance().createTables();
                            if (!result) {
                                Logger.logError(fileName, "login", "Error creating database tables for browser");
                                cbResult.type = LoginListenerType.auth_activation_error;
                                cbResult.error = "Error creating database tables for browser";
                                resolve(cbResult); 
                                return;
                            }
                        }
                    }

                    UnviredAccountManager.getInstance().setLastLoggedOutAccount(null)
                    UnviredAccountManager.getInstance().setLastLoggedInAccount(lastActiveAccount.getAccountId())
                    this.loginParameters.username = this.loginParameters.username ?? lastActiveAccount.getUsername();
                    this.loginParameters.company = this.loginParameters.company ?? lastActiveAccount.getCompany();
                    this.loginParameters.url = this.loginParameters.url ?? lastActiveAccount.getServerURL();
                    this.loginParameters.feUserId = this.loginParameters.feUserId ?? lastActiveAccount.getFeUserId();
                    this.loginParameters.loginType = this.loginParameters.loginType ?? lastActiveAccount.getLoginType();
                    this.loginParameters.port = this.loginParameters.port ?? lastActiveAccount.getPort();
                    this.loginParameters.domain = this.loginParameters.domain ?? lastActiveAccount.getDomain();
                    FrameworkSettingsManager.getInstance().setFieldValue(FrameworkSettingsFields.url, this.loginParameters.url);
                    FrameworkSettingsManager.getInstance().setFieldValue(FrameworkSettingsFields.feUser, this.loginParameters.feUserId);
                    cbResult.type = LoginListenerType.login_success;
                    OutboxService.getInstance().start();
                    GetMessageTimerManager.getInstance().startTimer();
                    if (FrameworkHelper.getPlatform() === "browser" || FrameworkHelper.getPlatform() === "electron") {
                        SsePushService.startPushService();
                    }
                    else if (FrameworkHelper.getPlatform() === "android" || FrameworkHelper.getPlatform() === "ios") {
                        this.registerAndListenForPushNotifications();
                    }
                    resolve(cbResult);
                    return
                }
            }
            catch (e) {
                let unviredResult = new UnviredResult()
                unviredResult.type = LoginListenerType.login_error
                unviredResult.message = e
                resolve(unviredResult)
            }
        });
    }

    public loginWithDemoData(loginParameters: any): Promise<any> {
        return new Promise(async (resolve, reject) => {
            const unviredResult = new UnviredResult();
            const frameworkSettings: any = FrameworkSettingsManager.getDemoModeFwSettings(loginParameters);
            const userSettings = UserSettingsManager.getDemoModeUserSettings(loginParameters);
            // Create FW Tables
            const result = await DatabaseManager.getInstance().createTables();
            if (!result) {
                Logger.logError(fileName, "authenticateAndActivate", "Error creating database tables");
                unviredResult.type = LoginListenerType.login_demo;
                unviredResult.error = "Error creating database tables";
                resolve(unviredResult);
                return;
            }
            Logger.logInfo(fileName, "authenticateAndActivate", "Database tables created successfully");

            // Save the settings in the DB
            try {
                frameworkSettings[FrameworkSettingsFields[FrameworkSettingsFields.url]] = loginParameters.url;
                frameworkSettings[FrameworkSettingsFields[FrameworkSettingsFields.feUser]] = loginParameters.feUserId;
                await this.saveSettings(frameworkSettings, userSettings);
            }
            catch (e) {
                Logger.logError(fileName, "authenticateAndActivate", `Error while saving settings. Error: ${e}`);
                unviredResult.type = LoginListenerType.login_demo;
                unviredResult.error = `Error while saving settings. Error: ${e}`;
                resolve(unviredResult);
                return;
            }
            Logger.logInfo(fileName, "authenticateAndActivate", "Settings saved successfully");
            resolve(unviredResult);
            
        });
    }
  
    public logout(): Promise<any> {
        return new Promise((resolve, reject) => {
            let result: any = {}
            const unviredAccount = UnviredAccountManager.getInstance().getAccount(this.loginParameters.username, this.loginParameters.company)
            if (unviredAccount == null) {
                Logger.logError(fileName, "logout", "Account not found to logout.");
                result.message = "Account not found to logout."
                result.type = 1;
                reject(result);
                return
            }
            UnviredAccountManager.getInstance().setLastLoggedOutAccount(unviredAccount.getAccountId())
            result.message = "Logged out successfully."
            result.type = 0;
            resolve(result);
        });
    }
  
    public clearData(): Promise<any> {
        return new Promise(async (resolve, reject) => {
            let result: any = {}
            try {
                GetMessageTimerManager.getInstance().stopTimer()
                const unviredAccount = UnviredAccountManager.getInstance().getAccount(this.loginParameters.username, this.loginParameters.company)
                if (unviredAccount == null) {
                    Logger.logError(fileName, "clearData", "Account not found to clear data.");
                    result.message = "Account not found to clear data."
                    result.error = "Account not found to clear data."
                    result.type = 1;
                    reject(result);
                    return
                }
                UnviredAccountManager.getInstance().deleteAccount(unviredAccount.getAccountId())
                try {
                    // await FrameworkHelper.deleteUserFolder(unviredAccount.getAccountId())
                    await window.UnviredDB.deleteUserData({userId: unviredAccount.getAccountId()})
                    Logger.logInfo(fileName, "clearData", "User data deleted successfully");
                    result.data = "Deleted all the data for this application from the device. Please exit the application by pressing the 'Home' button in your device."//"Data cleared successfully."
                    result.type = 0;
                    DatabaseManager.getInstance().clearInstance()
                    localStorage.clear()
                    NotificationListenerHelper.postDataSenderNotification(null, NotificationListnerType.app_reset);
                    resolve(result);
                }
                catch(error) {
                    Logger.logError(fileName, "clearData", "Error deleting user data: " + JSON.stringify(error));
                    result.message = error.toString()
                    result.error = error.toString()
                    result.type = 1;
                    reject(result);
                }
            }
            catch (e) {
                Logger.logError(fileName, "clearData", `Error while clearing data. Error: ${JSON.stringify(e)}`);
                result.message = e.toString()
                result.error = e.toString()
                result.type = 1;
                reject(result);
            }
        });
    }

    public authenticateAndActivate(loginParameters: any): Promise<any> {
        this.loginParameters = loginParameters
        return new Promise(async (resolve, reject) => {
            if (this.loginParameters.metadataPath != null && this.loginParameters.metadataPath.length > 0) {
                const metadataJSON = await HttpConnection.getJSONFromFile(this.loginParameters.metadataPath);
                this.loginParameters.metadataJSON = metadataJSON;
            }
            // Check #1: Check if the Account Already Exists.
            const accountObject = await UnviredAccountManager.getInstance().getAccount(loginParameters.username, loginParameters.company)
            let isAccountExist = accountObject != null
            if (isAccountExist) {
                Logger.logError(fileName, "login", "Account already in use on this device. Go back and choose this account.");
                let cbResult: any = {}
                cbResult.message = "Account already in use on this device. Go back and choose this account."
                cbResult.type = LoginListenerType.auth_activation_error;
                resolve(cbResult);
                return
            }
            const unviredResult = new UnviredResult();
            var frameworkSettings: any = {}
            var userSettings: any = {}
            if (loginParameters.loginType !== LoginType.saml2) {
                const result = await HttpConnection.authenticateUser(this.loginParameters);
                const responseJson = await result.json();
                unviredResult.type = result.ok ? LoginListenerType.auth_activation_success : LoginListenerType.auth_activation_error;
                if (result.ok) {
                    unviredResult.data = responseJson;
                } else {
                    unviredResult.error = responseJson.error;
                }
                if (result.status === Status.created) {
                    frameworkSettings = responseJson;
                    if (responseJson[ServiceConstants.KeySessionId] != null) {
                        HttpConnection.sessionId = responseJson[ServiceConstants.KeySessionId];
                    }
                    if (loginParameters.loginType === LoginType.sap || loginParameters.loginType === LoginType.ads) {
                        this.loginParameters.unviredUserName = responseJson[ServiceConstants.KeyUnviredId];
                    }
                    const userData: any[] = responseJson[ServiceConstants.KeyUsers];

                    const frontendIds: string[] = userData
                        .filter(
                            (element) =>
                                    (FrameworkHelper.getFrontendType() === "WINDOWS" ? 
                                        (element[ServiceConstants.KeyFrontendType] === "WINDOWS" || element[ServiceConstants.KeyFrontendType] === "WINDOWS8") :
                                        element[ServiceConstants.KeyFrontendType] === FrameworkHelper.getFrontendType()) &&
                                element[ServiceConstants.KeyApplications] != null &&
                                (element[ServiceConstants.KeyApplications] as any[])
                                    .filter((e) => e[ServiceConstants.KeyName] === loginParameters.appName)
                                    .length > 0
                        )
                        .map((e) => e[ServiceConstants.KeyName] as string);

                    if (frontendIds.length === 0) {
                        Logger.logError(fileName, "authenticateAndActivate", "Application is not deployed for this user.");
                        this.loginParameters.password = "";
                        unviredResult.type = LoginListenerType.auth_activation_error;
                        unviredResult.error = "Application is not deployed for this user. Please contact your Administrator.";
                        resolve(unviredResult);
                        return;
                    } else if (frontendIds.length > 1) {
                        // TODO: Check: How to send multiple frontend ids to the app.
                        // if (this.loginParameters.frontendId.isEmpty) {
                        //     _selectedAccount!.setErrorMessage(
                        //         "Multiple Frontends are available for this user. Please select one frontend to proceed.");
                        //     _selectedAccount!.setAvailableFrontendIds(frontendIds);
                        //     _selectedAccount!.setPassword("");
                        //     _selectedAccount = await _getSelectedFEFromApplication([_selectedAccount!]);
                        // } else if (!frontendIds.includes(_selectedAccount!.getFrontendId())) {
                        //     _selectedAccount!.setErrorMessage("Invalid frontend id.");
                        //     _selectedAccount!.setAvailableFrontendIds(frontendIds);
                        //     _selectedAccount!.setPassword("");
                        //     return await _callShowLoginAndValidateInput([_selectedAccount!]);
                        // }
                    } else {
                        this.loginParameters.feUserId = frontendIds[0];
                    }
                    // if (FrameworkHelper.getPlatform() !== "browser") {
                        // Call activate rest api
                        const activationResponse = await HttpConnection.activateUser(this.loginParameters);

                        // Handle Activation Response
                        const activationResponseObject = await activationResponse.json();
                        if (activationResponse.status === Status.created) {
                            frameworkSettings = activationResponseObject;
                            this.loginParameters.activationId = frameworkSettings[ServiceConstants.KeySettings]["activationId"];
                            if (loginParameters.loginType === LoginType.sap || loginParameters.loginType === LoginType.ads || loginParameters.loginType === LoginType.passwordless) {
                                this.loginParameters.unviredUserPwd = activationResponseObject[ServiceConstants.KeySettings][ServiceConstants.KeyUnviredMd5Pwd]
                            }
                            const unviredId = UserSettingsFields[UserSettingsFields.unviredId];
                            userSettings[unviredId] = loginParameters.username;
                            const unviredPwd = UserSettingsFields[UserSettingsFields.unviredPassword];

                            let pwdToSave = loginParameters.password;
                            if (loginParameters.loginType !== LoginType.ads && loginParameters.loginType !== LoginType.sap) {
                                pwdToSave = FrameworkHelper.getMD5String(loginParameters.password);
                            }
                            userSettings[unviredPwd] = pwdToSave;
                        } else {
                            let errorMsg = "Login Failed. Error: ";
                            if (activationResponseObject[ServiceConstants.KeyError] != null && activationResponseObject[ServiceConstants.KeyError].length > 0) {
                                errorMsg += activationResponseObject[ServiceConstants.KeyError];
                            }
                            Logger.logError(fileName, "authenticateAndActivate", errorMsg);
                            unviredResult.type = LoginListenerType.auth_activation_error;
                            unviredResult.error = errorMsg;
                            resolve(unviredResult);
                            return;
                        }
                    // } else {
                    //     const unviredId = UserSettingsFields[UserSettingsFields.unviredId];
                    //     userSettings[unviredId] = loginParameters.username;
                    //     const unviredUserId = UserSettingsFields[UserSettingsFields.unviredUserId];
                    //     userSettings[unviredUserId] = frameworkSettings[unviredId];
                    //     const unviredPwd = UserSettingsFields[UserSettingsFields.unviredPassword];
                    //     let pwdToSave = loginParameters.password;
                    //     if (loginParameters.loginType !== LoginType.ads && loginParameters.loginType !== LoginType.sap) {
                    //         pwdToSave = FrameworkHelper.getMD5String(loginParameters.password);
                    //     }
                    //     userSettings[unviredPwd] = pwdToSave;
                    //     // added this to save the settings in the DB
                    //     frameworkSettings = {"settings": frameworkSettings}
                    // }

                    // Get jwt token from rest api
                    const jwtResponse = await HttpConnection.getJwtToken(this.loginParameters);
                    // Handle jwt Response
                    const jwtResponseObject = await jwtResponse.json();
                    if (jwtResponse.status !== Status.created) {
                        let errorMsg = "Login Failed. Error: ";
                        if (
                            jwtResponseObject[ServiceConstants.KeyError] != null &&
                            jwtResponseObject[ServiceConstants.KeyError].length > 0
                        ) {
                            errorMsg += jwtResponseObject[ServiceConstants.KeyError];
                        }
                        Logger.logError(fileName, "authenticateAndActivate", errorMsg);
                        unviredResult.type = LoginListenerType.auth_activation_error;
                        unviredResult.error = errorMsg;
                        resolve(unviredResult);
                        return;
                    }
                    const jwtToken = UserSettingsFields[UserSettingsFields.jwtToken];
                    userSettings[jwtToken] = jwtResponseObject[ServiceConstants.KeyToken];
                    loginParameters.jwtToken = jwtResponseObject[ServiceConstants.KeyToken];
                }
                else {
                    resolve(unviredResult);
                    return;
                }
            } else {
                // The Browser check is commented out for SAML2 login type.Because it should always call the activate rest api.
                // if (FrameworkHelper.getPlatform() !== "browser") {
                    // Call activate rest api
                    const activationResponse = await HttpConnection.activateUser(loginParameters);

                    // Handle Activation Response
                    const activationResponseObject = await activationResponse.json();
                    if (activationResponse.status === Status.created) {
                        frameworkSettings = activationResponseObject;
                        loginParameters.frontendId = frameworkSettings[ServiceConstants.KeySettings]["frontendId"];
                        loginParameters.feUserId = frameworkSettings[ServiceConstants.KeySettings]["feUser"];
                        loginParameters.activationId = frameworkSettings[ServiceConstants.KeySettings]["activationId"];
                        const unviredId = UserSettingsFields[UserSettingsFields.unviredId];
                        userSettings[unviredId] = loginParameters.username;
                        const unviredPwd = UserSettingsFields[UserSettingsFields.unviredPassword];
                        userSettings[unviredPwd] = FrameworkHelper.getMD5String(loginParameters.password);
                    } else {
                        let errorMsg = "Login Failed. Error: ";
                        if (
                            activationResponseObject[ServiceConstants.KeyError] != null &&
                            activationResponseObject[ServiceConstants.KeyError].length > 0
                        ) {
                            errorMsg += activationResponseObject[ServiceConstants.KeyError];
                        }
                        Logger.logError(fileName, "authenticateAndActivate", errorMsg);
                        unviredResult.type = LoginListenerType.auth_activation_error;
                        unviredResult.error = errorMsg;
                        resolve(unviredResult);
                        return;
                    }

                // } else {
                //     const unviredId = UserSettingsFields[UserSettingsFields.unviredId];
                //     userSettings[unviredId] = loginParameters.username;
                //     const unviredUserId = UserSettingsFields[UserSettingsFields.unviredUserId];
                //     userSettings[unviredUserId] = frameworkSettings[unviredId];
                //     const unviredPwd = UserSettingsFields[UserSettingsFields.unviredPassword];
                //     userSettings[unviredPwd] = FrameworkHelper.getMD5String(loginParameters.password);
                //     // added this to save the settings in the DB
                //     frameworkSettings = {"settings": frameworkSettings}
                // }


                // NOTE: This is not required as the jwt token is already available in the loginParameters.
                // // Get jwt token from rest api
                // const jwtResponse = await HttpConnection.getJwtToken(loginParameters);
                // // Handle jwt Response
                // const jwtResponseObject = await jwtResponse.json();
                // if (jwtResponse.status !== Status.unauthorized) {
                //     let errorMsg = "Login Failed. Error: ";
                //     if (jwtResponseObject[ServiceConstants.KeyError] != null && jwtResponseObject[ServiceConstants.KeyError].length > 0) {
                //         errorMsg += jwtResponseObject[ServiceConstants.KeyError];
                //     }
                //     Logger.logError(fileName, "authenticateAndActivate", errorMsg);
                //     unviredResult.type = LoginListenerType.auth_activation_error;
                //     unviredResult.error = errorMsg;
                //     resolve(unviredResult);
                //     return;
                // }

                unviredResult.type = LoginListenerType.auth_activation_success;
                
                const jwtToken = UserSettingsFields[UserSettingsFields.jwtToken];
                userSettings[jwtToken] = loginParameters["jwtToken"];
            }

            const metadataParser = ApplicationMetaParser.instance;
            await metadataParser.init(loginParameters.metadataJSON);

            // Save the Unvired Account in the local storage.
            const unviredAccount = new UnviredAccount(this.loginParameters.username, this.loginParameters.company, this.loginParameters.url, this.loginParameters.feUserId, this.loginParameters.loginType, this.loginParameters.jwtToken)
            unviredAccount.setPort(this.loginParameters.port ?? "")
            unviredAccount.setDomain(this.loginParameters.domain ?? "")

            UnviredAccountManager.getInstance().saveAccount(unviredAccount, loginParameters.appName)
            UnviredAccountManager.getInstance().setLastLoggedOutAccount(null)
            UnviredAccountManager.getInstance().setLastLoggedInAccount(unviredAccount.getAccountId())

            // Create FW Tables
            const result = await DatabaseManager.getInstance().createTables();
            if (!result) {
                UnviredAccountManager.getInstance().deleteAccount(unviredAccount.getAccountId())
                UnviredAccountManager.getInstance().setLastLoggedOutAccount(null)
                UnviredAccountManager.getInstance().setLastLoggedInAccount(null)
                Logger.logError(fileName, "authenticateAndActivate", "Error creating database tables");
                unviredResult.type = LoginListenerType.auth_activation_error;
                unviredResult.error = "Error creating database tables";
                resolve(unviredResult);
                return;
            }
            Logger.logInfo(fileName, "authenticateAndActivate", "Database tables created successfully");

            // Save the settings in the DB
            try {
                frameworkSettings[FrameworkSettingsFields[FrameworkSettingsFields.url]] = loginParameters.url;
                frameworkSettings[FrameworkSettingsFields[FrameworkSettingsFields.feUser]] = loginParameters.feUserId;
                await this.saveSettings(frameworkSettings, userSettings);
            }
            catch (e) {
                UnviredAccountManager.getInstance().deleteAccount(unviredAccount.getAccountId())
                UnviredAccountManager.getInstance().setLastLoggedOutAccount(null)
                UnviredAccountManager.getInstance().setLastLoggedInAccount(null)
                Logger.logError(fileName, "authenticateAndActivate", `Error while saving settings. Error: ${e}`);
                unviredResult.type = LoginListenerType.auth_activation_error;
                unviredResult.error = `Error while saving settings. Error: ${e}`;
                resolve(unviredResult);
                return;
            }
            Logger.logInfo(fileName, "authenticateAndActivate", "Settings saved successfully");
            if (unviredResult.type === LoginListenerType.auth_activation_success) {
                OutboxService.getInstance().start();
                GetMessageTimerManager.getInstance().startTimer();
                if (FrameworkHelper.getPlatform() === "browser" || FrameworkHelper.getPlatform() === "electron") {
                    SsePushService.startPushService();
                }
                else if (FrameworkHelper.getPlatform() === "android" || FrameworkHelper.getPlatform() === "ios") {
                    this.registerAndListenForPushNotifications();
                }
            }
            resolve(unviredResult);
        });
    }

    public async authenticateLocal(loginParameters: any): Promise<any> {
        return new Promise(async (resolve, reject) => {
            const unviredResult = new UnviredResult();
            const unviredAccount = UnviredAccountManager.getInstance().getAccount(loginParameters.username, loginParameters.company)
            if (unviredAccount == null) {
                Logger.logError(fileName, "authenticateLocal", "Account not found.");
                unviredResult.type = LoginListenerType.login_error;
                unviredResult.error = "Account information not found.";
                resolve(unviredResult);
                return;
            }
            if (navigator.onLine) {
                // Get jwt token from rest api
                try {
                    const jwtResponse = await HttpConnection.getJwtToken(loginParameters);
                    // Handle jwt Response
                    const jwtResponseObject = JSON.parse(jwtResponse.body);
                    if (jwtResponse.status !== Status.created) {
                        let errorMsg = "Login Failed. Error: ";
                        if (
                            jwtResponseObject[ServiceConstants.KeyError] != null &&
                            jwtResponseObject[ServiceConstants.KeyError].length > 0
                        ) {
                            errorMsg += jwtResponseObject[ServiceConstants.KeyError];
                        }
                        Logger.logError(fileName, "authenticateLocal", errorMsg);
                        unviredResult.type = LoginListenerType.login_error;
                        unviredResult.error = errorMsg;
                        resolve(unviredResult);
                        return;
                    }
                    const jwtToken = jwtResponseObject[ServiceConstants.KeyToken];
                    await UserSettingsManager.getInstance().setFieldValue(UserSettingsFields.jwtToken, jwtToken);
                    unviredAccount.setJwtToken(jwtToken)
                }
                catch (e) {
                    Logger.logError(fileName, "authenticateLocal", `Error while getting jwt token. Error: ${JSON.stringify(e)}`);
                    unviredResult.type = LoginListenerType.login_error;
                    unviredResult.error = `Login failed. Error: ${JSON.stringify(e)}`;
                    resolve(unviredResult);
                    return;
                }
            }
            else {
                const storedPassword = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.unviredPassword)
                const md5Password = FrameworkHelper.getMD5String(loginParameters.password)
                if (storedPassword !== md5Password) {
                    Logger.logError(fileName, "authenticateLocal", "Invalid password.");
                    unviredResult.type = LoginListenerType.login_error;
                    unviredResult.error = "Invalid password."
                    resolve(unviredResult)
                    return
                }
            }
            UnviredAccountManager.getInstance().saveAccount(unviredAccount, loginParameters.appName)
            UnviredAccountManager.getInstance().setLastLoggedOutAccount(null)
            UnviredAccountManager.getInstance().setLastLoggedInAccount(unviredAccount.getAccountId())

            if(FrameworkHelper.getPlatform() === "browser") {
                const metadataParser = ApplicationMetaParser.instance;
                await metadataParser.init(loginParameters.metadataJSON);
                const isDBAvailable = await this.checkIfDataPresentInIndexDB(unviredAccount.getAccountId())
                if (!isDBAvailable) {                      
                    // Create FW Tables
                    const result = await DatabaseManager.getInstance().createTables();
                    if (!result) {
                        Logger.logError(fileName, "authenticateLocal", "Error creating database tables for browser");
                        unviredResult.type = LoginListenerType.login_error;
                        unviredResult.error = "Error creating database tables for browser";
                        resolve(unviredResult); 
                        return;
                    }
                }
                // Create FW Tables
                const result = await DatabaseManager.getInstance().createTables();
                if (!result) {
                    Logger.logError(fileName, "authenticateLocal", "Error creating database tables for browser");
                    unviredResult.type = LoginListenerType.login_error;
                    unviredResult.error = "Error creating database tables for browser";
                    resolve(unviredResult); 
                    return;
                }
            }
            unviredResult.type = LoginListenerType.login_success;
            OutboxService.getInstance().start();
            GetMessageTimerManager.getInstance().startTimer();
            if (FrameworkHelper.getPlatform() === "browser" || FrameworkHelper.getPlatform() === "electron") {
                SsePushService.startPushService();
            }
            else if (FrameworkHelper.getPlatform() === "android" || FrameworkHelper.getPlatform() === "ios") {
                this.registerAndListenForPushNotifications();
            }
            resolve(unviredResult);
        });
    }
  
    public getAllAccounts(): Promise<any> {
        return new Promise((resolve, reject) => {
            try {
                const allAccounts = UnviredAccountManager.getInstance().getAllAccounts();
                resolve(allAccounts);
            }
            catch (e) {
                Logger.logError(fileName, "getAllAccounts", `Error while getting all accounts. Error: ${JSON.stringify(e)}`);
                reject(e);
            }
        });
    }
  
    public switchAccount(account: any): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
  
    public deleteAccount(account: any): Promise<any> {
        return new Promise(async (resolve, reject) => {
            try {
                UnviredAccountManager.getInstance().deleteAccount(account.getAccountId());
                await FrameworkHelper.deleteUserFolder(account.getAccountId())
                resolve(true);
            }
            catch (e) {
                Logger.logError(fileName, "deleteAccount", `Error while deleting account. Error: ${JSON.stringify(e)}`);
                reject(e);
            }
        });
    }
  
    public setClientCredentials(credentials: any[]): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }
  
    public isClientCredentialsSet(): Promise<any> {
        return new Promise((resolve, reject) => {
            // TODO:
            let isSuccessful = true
            if (isSuccessful) {
                const result = { /* data to be returned on success */ };
                resolve(result);
            } else {
                const error = { /* error message */ };
                reject(error);
            }
        });
    }

    public setMessageInterval(interval: number): void {
        this.messageInterval = interval;
    }

    public getMessageInterval(): number {
        return this.messageInterval;
    }

    private async saveSettings(frameworkSettings: any, userSettings: any): Promise<any> {
        if (Object.keys(frameworkSettings).length > 0) {
            // Parse and insert framework data into database
            const fwSettingsManager = FrameworkSettingsManager.getInstance()
            const fwSettings: any = frameworkSettings[ServiceConstants.KeySettings];
            if (fwSettings != null) {
                const unviredAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
                const fwSettingsKeys: string[] = Object.keys(fwSettings);
                for (const key of fwSettingsKeys) {
                    const value = fwSettings[key];
                    if (key == FrameworkSettingsFields[FrameworkSettingsFields.localPassword] && value === "YES") {
                        // TODO: Set Local Password required field to true in Unvired Account
                    }
                    if (key === FrameworkSettingsFields[FrameworkSettingsFields.logLevel]) {
                        SettingsHelper.setLogLevel(value);
                    }
            
                    if (key === FrameworkSettingsFields[FrameworkSettingsFields.firstName]) {
                        unviredAccount.setFirstName(value);
                    }

                    if (key === FrameworkSettingsFields[FrameworkSettingsFields.lastName]) {
                        unviredAccount.setLastName(value);
                    }

                    if (key === ServiceConstants.KeySystems) {
                        const systemInfo: { [key: string]: any } = value;
                        const systemInfoArray: any[] = systemInfo[ServiceConstants.KeySystems];
                        for (const systemInfoObj of systemInfoArray) {
                            const timestamp: number = Date.now();
                            const systemCredentials: any = {
                                "lid": FrameworkHelper.getUUID(),
                                "timestamp": timestamp,
                                "objectStatus": ObjectStatus.global,
                                "syncStatus": SyncStatus.none,
                                "name": systemInfoObj[ServiceConstants.KeySystemsName],
                                "portName": systemInfoObj[ServiceConstants.KeySystemsPortName],
                                "portType":  systemInfoObj[ServiceConstants.KeySystemsPortType],
                                "portDesc": systemInfoObj[ServiceConstants.KeySystemsPortDesc],
                                "systemDesc": systemInfoObj[ServiceConstants.KeySystemsDesc],
                                "userId":"", 
                                "password": ""
                            };
                            await fwSettingsManager.addSystemCredential(systemCredentials);
                        }
                    }
                    else if (key === ServiceConstants.KeyLocation) {
                        const locationInfo: { [key: string]: any } = value;
                        const locationInfoKeys: string[] = Object.keys(locationInfo);
                        for (const key2 of locationInfoKeys) {
                            const value2 = locationInfo[key2];
                            switch (key2) {
                                case ServiceConstants.KeyLocationTracking:
                                    await fwSettingsManager.setFieldValue(FrameworkSettingsFields.locationTracking, value2);
                                    break;
                                case ServiceConstants.KeyLocationInterval:
                                    await fwSettingsManager.setFieldValue(FrameworkSettingsFields.locationTrackingInterval, value2);
                                    break;
                                case ServiceConstants.KeyLocationUploadInterval:
                                    await fwSettingsManager.setFieldValue(FrameworkSettingsFields.locationUploadInterval, value2);
                                    break;
                                case ServiceConstants.KeyLocationDays:
                                    await fwSettingsManager.setFieldValue(FrameworkSettingsFields.locationTrackingDays, value2);
                                    break;
                                case ServiceConstants.KeyLocationStart:
                                    await fwSettingsManager.setFieldValue(FrameworkSettingsFields.locationTrackingStart, value2);
                                    break;
                                case ServiceConstants.KeyLocationEnd:
                                    await fwSettingsManager.setFieldValue(FrameworkSettingsFields.locationTrackingEnd, value2);
                                    break;
                            }
                        }
                    }
                    else if (key === ServiceConstants.KeyAppSettings) {
                        const appSettings: any[] = value;
                        for (const appSetting of appSettings) {
                            const appSettingObject = {
                                lid: FrameworkHelper.getUUID(),
                                timestamp: Date.now(),
                                objectStatus: ObjectStatus.global,
                                syncStatus: SyncStatus.none,
                                keyName: appSetting.key,
                                description: appSetting.value,
                                defaultField: "",
                                current: "",
                                mandatory: "",
                                secure: ""
                            };
                            await DatabaseManager.getInstance().insertOrUpdate(DatabaseType.FrameworkDb, "MobileUserSettings", appSettingObject, true);
                        }
                    }
                    else if (key === FrameworkSettingsFields[FrameworkSettingsFields.unviredUser]) {
                        const unviredUserId = UserSettingsFields[UserSettingsFields.unviredUserId];
                        userSettings[unviredUserId] = value;
                    }
                    else {
                        if (typeof value !== 'object') {
                            await fwSettingsManager.setFieldValue(stringToFrameworkSettingsFields(key), value ?? "");
                        }
                    }
                }
                UnviredAccountManager.getInstance().saveAccount(unviredAccount, this.loginParameters.appName)
            }
        }
        
        if (Object.keys(userSettings).length > 0) {
            // Parse and insert user data into database
            const userSettingsManager = UserSettingsManager.getInstance()
            const userSettingsKeys: string[] = Object.keys(userSettings);
            for (const key of userSettingsKeys) {
                const value = userSettings[key];
                await userSettingsManager.setFieldValue(stringToUserSettingsFields(key), value);
            }
        }
          
    }

    private async checkIfDataPresentInIndexDB(userId: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            const fwDbName = userId + '_fw_db';
            const request = window.indexedDB.open(fwDbName);
            request.onsuccess = function(event) {
                const db = request.result;
                if (!db.objectStoreNames.contains('fwData')) {
                    resolve(false); // Object store does not exist
                    return;
                }
                const transaction = db.transaction(['fwData'], 'readonly');
                const objectStore = transaction.objectStore('fwData');
                const countRequest = objectStore.count();
                countRequest.onsuccess = function() {
                    resolve(countRequest.result > 0);
                };
                countRequest.onerror = function() {
                    resolve(false);
                };
            };
            request.onerror = function() {
                resolve(false);
            };
        });
    }

    /**
     * Register and listen for push notifications using FirebasePushService (for Android/iOS platforms).
     * This function is reusable and can be called wherever push notification registration is needed.
     */
    private registerAndListenForPushNotifications(): void {
        try {
            FirebasePushService.registerForPushNotifications(
                async (token: string) => {
                    Logger.logInfo(fileName, "login", `Push registration token: ${token}`);
                    // You can send this token to your server if needed
                    try {
                        const result = await HttpConnection.registerNotification(token);
                        if (!result.ok) {
                            Logger.logError("SsePushService", "startPushService", "Error: " + JSON.stringify(result));
                            return;
                        }
                    } catch (error) {
                        Logger.logError("SsePushService", "startPushService", "Error: " + JSON.stringify(error));
                        return;
                    }
                },
                (error: any) => {
                    Logger.logError(fileName, "login", `Push registration error: ${error}`);
                }
            );
            FirebasePushService.onNotificationReceived(
                (data: any) => {
                    Logger.logInfo(fileName, "login", `Push notification received: ${JSON.stringify(data)}`);
                    // Handle notification data as needed
                },
                (error: any) => {
                    Logger.logError(fileName, "login", `Push notification error: ${error}`);
                }
            );
        } catch (e) {
            Logger.logError(fileName, "login", `Failed to initialize FirebasePushService: ${e}`);
        }
    }
}

export default AuthenticationService.instance