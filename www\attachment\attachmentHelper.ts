import { UnviredAccountManager } from "../helper/unviredAccountManager";
import FrameworkHelper from "../helper/frameworkHelper";
import DatabaseManager, { DatabaseType } from "../database/databaseManager";

declare var Logger: any;

const fileName = "AttachmentHelper";

export default class AttachmentHelper {
  private static readonly ATTACHMENT_DIR = 'Attachment/'; 

  // Helper method to create directories recursively
  private static async createDirectoriesRecursively(dirPath: string): Promise<string> {
      return new Promise((resolve, reject) => {
          // Split the path into parts
          const pathParts = dirPath.split(/[\\\/]/).filter(part => part.length > 0);
          let currentPath = '';
          
          // Handle Windows drive letter
          if (pathParts.length > 0 && pathParts[0].includes(':')) {
              currentPath = pathParts[0] + '/';
              pathParts.shift();
          }
          
          const createNextDirectory = (index: number) => {
              if (index >= pathParts.length) {
                  // All directories created, resolve with the final path
                  resolve(dirPath);
                  return;
              }
              
              currentPath += pathParts[index] + '/';
              
              window.resolveLocalFileSystemURL(currentPath, 
                  (dirEntry) => {
                      // Directory exists, continue to next
                      Logger.logInfo(fileName, "createDirectoriesRecursively", "Directory exists: " + currentPath);
                      createNextDirectory(index + 1);
                  },
                  (error) => {
                      // Directory doesn't exist, create it
                      Logger.logInfo(fileName, "createDirectoriesRecursively", "Creating directory: " + currentPath);
                      
                      // Get parent directory
                      const trimmedPath = currentPath.replace(/\/$/, ''); // Remove trailing slash if present
                      const parentPath = trimmedPath.substring(0, trimmedPath.lastIndexOf('/'));
                      window.resolveLocalFileSystemURL(parentPath, 
                          (parentDir) => {
                              parentDir.getDirectory(pathParts[index], { create: true }, 
                                  (newDir) => {
                                      Logger.logInfo(fileName, "createDirectoriesRecursively", "Directory created: " + newDir.fullPath);
                                      createNextDirectory(index + 1);
                                  },
                                  (createError) => {
                                      Logger.logError(fileName, "createDirectoriesRecursively", "Failed to create directory: " + currentPath + " Error: " + createError);
                                      reject(createError);
                                  }
                              );
                          },
                          (parentError) => {
                              Logger.logError(fileName, "createDirectoriesRecursively", "Failed to get parent directory: " + parentPath + " Error: " + parentError);
                              reject(parentError);
                          }
                      );
                  }
              );
          };
          
          createNextDirectory(0);
      });
  }

  static getAttachmentDirectory(): Promise<string> {
    return new Promise(async (resolve, reject) => {
      if (FrameworkHelper.getPlatform() === "electron") {
        const mobileUserSettings: any[] = await DatabaseManager.getInstance().select(DatabaseType.FrameworkDb, "MobileUserSettings", "keyName = 'ATTACHMENT_STORAGE_PATH'" );
        if (mobileUserSettings.length > 0) {
          const attachmentStoragePath = mobileUserSettings[0].description;

          // Check if the path exists, if not create directories recursively
          try {
              const pathExists = await this.fileExists(attachmentStoragePath);
              if (pathExists) {
                  Logger.logInfo(fileName, "getAttachmentDirectory", "Path exists: " + attachmentStoragePath);
                  resolve(attachmentStoragePath);
              } else {
                  Logger.logInfo(fileName, "getAttachmentDirectory", "Path does not exist, creating directories: " + attachmentStoragePath);
                  const createdPath = await this.createDirectoriesRecursively(attachmentStoragePath);
                  resolve(createdPath);
              }
          } catch (error) {
              Logger.logError(fileName, "getAttachmentDirectory", "Error checking/creating path: " + error);
              resolve("");
          }
          return;
        }
      }
      
      const account = UnviredAccountManager.getInstance().getLastLoggedInAccount()
      const userDirectory = await FrameworkHelper.getFolderBasedOnUserId(account.getAccountId());
      const inboxDir = `${userDirectory}${AttachmentHelper.ATTACHMENT_DIR}`;
      window.resolveLocalFileSystemURL(inboxDir, (dir) => {
          Logger.logInfo(fileName, "getAttachmentDirectory", "Directory exists: " + dir.fullPath);
          resolve(dir.nativeURL);
      }, (error) => {
          Logger.logInfo(fileName, "getAttachmentDirectory", "Directory did not exist. Error: " + error);
          // Directory did not exist, so creating it.
          Logger.logInfo(fileName, "getAttachmentDirectory", "Directory did not exist, so creating it: " + inboxDir);
          window.resolveLocalFileSystemURL(userDirectory, (parentDir) => {
              parentDir.getDirectory(AttachmentHelper.ATTACHMENT_DIR, {create: true}, (newDir) => {
                  Logger.logInfo(fileName, "getAttachmentDirectory", "Directory created: " + newDir.nativeURL);
                  resolve(newDir.nativeURL);
              }, (error) => {
                  Logger.logError(fileName, "getAttachmentDirectory", "Unable to create Attachment directory. Error: " + error);
                  resolve("");
              })
          }, (error) => {
              Logger.logError(fileName, "getAttachmentDirectory", "Unable to get Attachment directory. Error: " + error);
              resolve("");
          })
      })
    })
  }

  static addAttachment(attachmentName: string, attachmentData: ArrayBuffer): Promise<string> {
    return new Promise(async (resolve, reject) => {
        const attachmentDir = await this.getAttachmentDirectory();
        window.resolveLocalFileSystemURL(attachmentDir, (dirEntry) => {
            dirEntry.getFile(`${attachmentName}`, { create: true }, (fileEntry) => {
              fileEntry.createWriter((fileWriter) => {
                      fileWriter.onwriteend = () => {
                          Logger.logInfo("AttachmentHelper", "addAttachment", "Write completed");
                          resolve(fileEntry.nativeURL);
                      };
                      fileWriter.onerror = (e) => {
                          Logger.logError("AttachmentHelper", "addAttachment", "Write error: " + e);
                          reject(e);
                      };
                      const blob = new Blob([attachmentData]);
                      fileWriter.write(blob);
                  },
                  (error) => {
                      Logger.logError("AttachmentHelper", "addAttachment", "Create writer error: " + error);
                      reject(error);
                  }
              );
            },
            (error) => {
                Logger.logError("AttachmentHelper", "addAttachment", "Get file error: " + error);
                reject(error);
            });
        },
        (error) => {
            Logger.logError("AttachmentHelper", "addAttachment", "Resolve file system URL error: " + error);
            reject(error);
        });
    });
  }

  static getAttachment(attachmentName: string): Promise<ArrayBuffer> {
    return new Promise(async (resolve, reject) => {
      const attachmentDir = await this.getAttachmentDirectory();
      window.resolveLocalFileSystemURL(attachmentDir, (dirEntry) => {
        dirEntry.getFile(`${attachmentName}`, { create: false }, (fileEntry) => {
          fileEntry.file((file) => {
            const reader = new FileReader();
            reader.onloadend = () => {
              Logger.logInfo("AttachmentHelper", "getAttachment", "Read completed");
              resolve(reader.result as ArrayBuffer);
            };
            reader.onerror = (e) => {
              Logger.logError("AttachmentHelper", "getAttachment", "Read error: " + e);
              reject(e);
            };
            reader.readAsArrayBuffer(file);
          }, 
          (error) => {
            Logger.logError("AttachmentHelper", "getAttachment", "Get file error: " + error);
            reject(error);
          });
        },
        (error) => {
          Logger.logError("AttachmentHelper", "getAttachment", "Get file error: " + error);
          reject(error);
        });
      }, 
      error => {
        Logger.logError("AttachmentHelper", "getAttachment", "Resolve file system URL error: " + error);
        reject(error);
      });
    });
  }

  static deleteAttachment(attachmentName: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      const attachmentDir = await this.getAttachmentDirectory();
      window.resolveLocalFileSystemURL(attachmentDir, (dirEntry) => {
        dirEntry.getFile(`${attachmentName}`, { create: false }, (fileEntry) => {
          fileEntry.remove(() => {
              Logger.logInfo("AttachmentHelper", "deleteAttachment", "File removed");
              resolve();
            },
            (error) => {
              Logger.logError("AttachmentHelper", "deleteAttachment", "Remove file error: " + error);
              reject(error);
            }
          );
        },
        (error) => {
          Logger.logError("AttachmentHelper", "deleteAttachment", "Get file error: " + error);
          reject(error);
        });
      },
      (error) => {
        Logger.logError("AttachmentHelper", "deleteAttachment", "Resolve file system URL error: " + error);
        reject(error);
      });
    });
  }

  static readFileAsArrayBuffer(filePath: string): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      window.resolveLocalFileSystemURL(filePath, (fileEntry) => {
        fileEntry.file((file) => {
          const reader = new FileReader();
          reader.onloadend = function() {
            Logger.logInfo("AttachmentHelper", "readFileAsArrayBuffer", "Read completed");
            resolve(this.result as ArrayBuffer);
          };
          reader.onerror = function(err) {
            Logger.logError("AttachmentHelper", "readFileAsArrayBuffer", "Read error: " + err);
            reject(err);
          };
          reader.readAsArrayBuffer(file);
        },
        (error) => {
          Logger.logError("AttachmentHelper", "readFileAsArrayBuffer", "Get file error: " + error);
          reject(error);
        });
      }, 
      (error) => {
        Logger.logError("AttachmentHelper", "readFileAsArrayBuffer", "Resolve file system URL error: " + error);
        reject(error);
      });
    });
  }

  static async fileExists(filePath: string): Promise<boolean> {
      return new Promise((resolve) => {
          window.resolveLocalFileSystemURL(filePath, 
              () => resolve(true), // File exists
              () => resolve(false) // File doesn't exist
          );
      });
  }

  // Mainly used for electron/windows platform.
  static async readExternalFile(filePath: string): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
        window.resolveLocalFileSystemURL(filePath, (fileEntry) => {
            fileEntry.file((file) => {
                const reader = new FileReader();
                reader.onloadend = function() {
                    Logger.logInfo(fileName, "readExternalFile", "Read completed");
                    resolve(this.result as ArrayBuffer);
                };
                reader.onerror = function(err) {
                    Logger.logError(fileName, "readExternalFile", "Read error: " + err);
                    reject(err);
                };
                reader.readAsArrayBuffer(file);
            }, 
            (error) => {
                Logger.logError(fileName, "readExternalFile", "Get file error: " + error);
                reject(error);
            });
        }, 
        (error) => {
            Logger.logError(fileName, "readExternalFile", "Resolve file system URL error: " + error);
            reject(error);
        });
    });
  }

  // Mainly used for electron/windows platform.
  static async writeExternalFile(filePath: string, fileName: string, data: ArrayBuffer): Promise<void> {
    return new Promise((resolve, reject) => {
      window.resolveLocalFileSystemURL(filePath, (dirEntry) => {
        dirEntry.getFile(`${fileName}`, { create: true }, (fileEntry) => {
          fileEntry.createWriter((fileWriter) => {
                  fileWriter.onwriteend = () => {
                      Logger.logInfo("AttachmentHelper", "writeExternalFile", "Write completed");
                      resolve();
                  };
                  fileWriter.onerror = (e) => {
                      Logger.logError("AttachmentHelper", "writeExternalFile", "Write error: " + e);
                      reject(e);
                  };
                  const blob = new Blob([data]);
                  fileWriter.write(blob);
              },
              (error) => {
                  Logger.logError("AttachmentHelper", "writeExternalFile", "Create writer error: " + error);
                  reject(error);
              }
          );
        },
        (error) => {
            Logger.logError("AttachmentHelper", "writeExternalFile", "Get file error: " + error);
            reject(error);
        });
    },
    (error) => {
        Logger.logError("AttachmentHelper", "writeExternalFile", "Resolve file system URL error: " + error);
        reject(error);
    });
    })
  }

  static async getAttachmentPath(attachmentName: string): Promise<string> {
    return new Promise(async (resolve, reject) => {
      const attachmentDir = await this.getAttachmentDirectory();
      resolve(`${attachmentDir}${attachmentName}`);
    });
  }
}
  