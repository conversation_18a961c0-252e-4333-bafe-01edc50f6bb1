import { FieldMeta, StructureMeta } from "../applicationMeta/applicationMetadataParser";
import FrameworkHelper from "../helper/frameworkHelper";
import { AttachmentBE } from "../helper/serviceConstants";
import { ObjectStatus, SyncStatus } from "../helper/utils";
import * as FieldConstants from '../applicationMeta/fieldConstants';
import { UnviredAccountManager } from "../helper/unviredAccountManager";
import * as ServiceConstants from '../helper/serviceConstants';
import AttachmentHelper from "../attachment/attachmentHelper";
import DbCreateTablesManager from "../helper/dbCreateTablesManager";

declare var Logger: any;

const constantFields = [
    FieldConstants.FieldLid,
    FieldConstants.FieldFid,
    FieldConstants.FieldConflict,
    FieldConstants.FieldTimestamp,
    FieldConstants.FieldObjectStatus,
    FieldConstants.FieldSyncStatus,
    FieldConstants.FieldInfoMsgCat
];

export enum DatabaseType {
    AppDb = "AppDb",
    FrameworkDb = "FrameworkDb"
}

export class DatabaseManager {
    private fileName = 'DatabaseManager'
    private static instance: DatabaseManager | null = null;
    private fieldMetas: FieldMeta[] = [];
    private isDbCreated: boolean = false;

    private constructor() {
        this.initializeDatabase()
    }

    private async initializeDatabase(): Promise<any> {
        return new Promise((resolve, reject) => {
            try {
                const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
                if (account == null) {
                    Logger.logInfo(this.fileName, "initializeDatabase", "Account not initialized");
                    resolve(false);
                    return;
                }
                if (this.isDbCreated) {
                    resolve(true);
                    return;
                }
                window.UnviredDB.create({
                    userId: account.getAccountId()
                }, (result: any) => {
                    Logger.logDebug(this.fileName, "initializeDatabase", "return data: " + JSON.stringify(result));
                    Logger.logInfo(this.fileName, "initializeDatabase", "Database created successfully. ");
                    this.isDbCreated = true;
                    resolve(true);
                }, (error: any) => {
                    Logger.logError(this.fileName, "initializeDatabase", "Error: " + error);
                    resolve(false);
                });
            }
            catch(e) {
                Logger.logError(this.fileName, 'initializeDatabase', e);
                resolve(false);
            }
        });
    }

    public static getInstance(): DatabaseManager {
        if (!DatabaseManager.instance) {
            DatabaseManager.instance = new DatabaseManager();
        }
        return DatabaseManager.instance;
    }

    public clearInstance() {
        DatabaseManager.instance = null
    }

    public async createTables(): Promise<boolean> { 
        const dbCreateTablesManager = new DbCreateTablesManager();
        return await dbCreateTablesManager.createTables();
    }

    public select(dbType:DatabaseType, tableName: string, whereClause: string = ""): Promise<any> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            var sqlQuery = `SELECT * FROM ${tableName}`
            if (whereClause != null && whereClause != "") {
                sqlQuery += ` WHERE ${whereClause}`
            }
            Logger.logDebug(newThis.fileName, 'select', 'sqlQuery: ' + sqlQuery);
            try {
                const result = await newThis.executeStatement(dbType, sqlQuery);
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'select', e);
                reject(e);
            }
        });
    }


    public insert(dbType: DatabaseType, tableName: string, structureObject: any, isHeader: boolean, isFromApp: boolean = false): Promise<any> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            const sqlQuery = await newThis.constructInsertOrReplaceQuery(tableName, structureObject, "INSERT", isHeader, dbType == DatabaseType.FrameworkDb, newThis, isFromApp);
            Logger.logDebug(newThis.fileName, 'insert', 'sqlQuery: ' + sqlQuery);
            try {
                const result = await newThis.executeStatement(dbType, sqlQuery);
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'insert', e);
                reject(e);
            }
        });
    }
  
    public insertOrUpdate(dbType: DatabaseType, tableName: string, structureObject: any, isHeader: boolean, isFromApp: boolean = false): Promise<any> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }

            const fieldMetas = await this.getFieldMetaDataList(newThis);

            const gidFieldMetas = fieldMetas.filter(element => element.structureName === tableName && element.isGid == "1");
            var whereClause = ""
            for (const fieldMeta of gidFieldMetas) {
                if (structureObject[fieldMeta.fieldName] != null) {
                    if (whereClause != "") {
                        whereClause += ` AND `
                    }
                    whereClause += `${fieldMeta.fieldName} = '${this.escapeSqlString(structureObject[fieldMeta.fieldName])}'`
                }
            }

            const existingObject = await newThis.select(dbType, tableName, whereClause);
            var sqlQuery = ""
            if (existingObject.length > 0) {
                sqlQuery = await newThis.constructUpdateQuery(tableName, structureObject, `${FieldConstants.FieldLid} = '${structureObject[FieldConstants.FieldLid]}'`, dbType == DatabaseType.FrameworkDb, newThis, isFromApp);
            }
            else {
                sqlQuery = await newThis.constructInsertOrReplaceQuery(tableName, structureObject, "INSERT", isHeader, dbType == DatabaseType.FrameworkDb, newThis, isFromApp);
            }
            Logger.logDebug(newThis.fileName, 'insertOrUpdate', 'sqlQuery: ' + sqlQuery);
            try {
                const result = await newThis.executeStatement(dbType, sqlQuery);
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'insertOrUpdate', e);
                reject(e);
            }
        });
    }
    
    public delete(dbType: DatabaseType, tableName: string, whereClause: string = ""): Promise<any> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            var sqlQuery = `DELETE FROM ${tableName}`
            if (whereClause != null && whereClause != "") {
                sqlQuery += ` WHERE ${whereClause}`
            }
            Logger.logDebug(newThis.fileName, 'delete', 'sqlQuery: ' + sqlQuery);
            try {
                const result = await newThis.executeStatement(dbType, sqlQuery);
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'delete', e);
                reject(e);
            }
        });
    }
    
    public update(dbType: DatabaseType, tableName: string, updatedObject: any, whereClause: any, isFromApp: boolean = false): Promise<any> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            const sqlQuery = await newThis.constructUpdateQuery(tableName, updatedObject, whereClause, dbType == DatabaseType.FrameworkDb, newThis, isFromApp);
            Logger.logDebug(newThis.fileName, 'update', 'sqlQuery: ' + sqlQuery);
            try {
                const result = await newThis.executeStatement(dbType, sqlQuery);
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'update', e);
                reject(e);
            }
        });
    }
    
    public executeStatement(dbType: DatabaseType, sqlQuery: string): Promise<any> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            try {
                const result = await newThis.execute(dbType, sqlQuery);
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'executeStatement', e);
                reject(e);
            }
        });
    }
    
    private execute(dbType: DatabaseType, query: string): Promise<any> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
            if (account == null) {
                Logger.logInfo(newThis.fileName, "execute", "Account not initialized");
                resolve("");
                return;
            }
            window.UnviredDB.execute({
                dbType: dbType,
                query: query,
                userId: account.getAccountId()
            }, (result: any) => {
                Logger.logDebug(newThis.fileName, "execute", "return data: " + JSON.stringify(result));
                Logger.logInfo(newThis.fileName, "execute", "Execute successfull. ");
                resolve(result);
            }, (error: any) => {
                Logger.logError(newThis.fileName, "execute", "Error: " + error);
                reject(error);
            });
        });
    }

    public createSavePoint(dbType: DatabaseType, savePoint: string): Promise<void> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            const sqlQuery = `SAVEPOINT ${savePoint}`
            try {
                const result = await newThis.execute(dbType, sqlQuery);
                Logger.logInfo(newThis.fileName, "createSavePoint", "Result: " + JSON.stringify(result));
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'createSavePoint', e);
                reject(e);
            }
        });
    }
    
    public releaseSavePoint(dbType: DatabaseType, savePoint: string): Promise<void> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            
            const sqlQuery = `RELEASE SAVEPOINT ${savePoint}`
            try {
                const result = await newThis.execute(dbType, sqlQuery);
                Logger.logInfo(newThis.fileName, "releaseSavePoint", "Result: " + JSON.stringify(result));
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'releaseSavePoint', e);
                reject(e);
            }
        });
    }
    
    public rollbackToSavePoint(dbType: DatabaseType, savePoint: string): Promise<void> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }

            const sqlQuery = `ROLLBACK TO SAVEPOINT ${savePoint}`
            try {
                const result = await newThis.execute(dbType, sqlQuery);
                Logger.logInfo(newThis.fileName, "rollbackToSavePoint", "Result: " + JSON.stringify(result));
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'rollbackToSavePoint', e);
                reject(e);
            }
        });
    }
    
    public beginTransaction(dbType: DatabaseType): Promise<void> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            
            const sqlQuery = `BEGIN TRANSACTION;`
            try {
                const result = await newThis.execute(dbType, sqlQuery);
                Logger.logInfo(newThis.fileName, "beginTransaction", "Result: " + JSON.stringify(result));
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'beginTransaction', e);
                reject(e);
            }
        });
    }
    
    public endTransaction(dbType: DatabaseType): Promise<void> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }
            
            const sqlQuery = `END TRANSACTION;`
            try {
                const result = await newThis.execute(dbType, sqlQuery);
                Logger.logInfo(newThis.fileName, "endTransaction", "Result: " + JSON.stringify(result));
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'endTransaction', e);
                reject(e);
            }
        });
    }

    public rollbackTransaction(dbType: DatabaseType): Promise<void> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            if (!this.isDbCreated) {
                const isInitialized = await newThis.initializeDatabase();
                if (!isInitialized) {
                    reject('Database not initialized');
                    return;
                }
            }

            const sqlQuery = `ROLLBACK;`
            try {
                const result = await newThis.execute(dbType, sqlQuery);
                Logger.logInfo(newThis.fileName, "rollbackTransaction", "Result: " + JSON.stringify(result));
                resolve(result);
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'rollbackTransaction', e);
                reject(e);
            }
        });
    }

    public async count(dbType: DatabaseType, tableName: string, whereClause: string = ""): Promise<number> {
        try {
            let sqlQuery = `SELECT COUNT(*) FROM ${tableName}`
            if (whereClause != null && whereClause != "") {
                sqlQuery += ` WHERE ${whereClause}`
            }
            const result = await this.executeStatement(dbType, sqlQuery);
            return result.length > 0 ? result[0]["COUNT(*)"] : 0;
        } catch (error) {
            Logger.logError(this.fileName, "count", "Error while counting records in table: ${tableName}. Error: " + error);
            return 0;
        }
    }

    public async getFirstEntity(dbType: DatabaseType, tableName: string): Promise<any> {
        try {
            const result = await this.executeStatement(dbType, `SELECT * FROM ${tableName} ORDER BY timestamp ASC LIMIT 1`);
            return result.length > 0 ? result[0] : null;
        } catch (error) {
            Logger.logError(this.fileName, "getFirstEntity", `Error while getting first record in table: ${tableName}. Error: ${error}`);
            return null;
        }
    }

    public saveWebData(): Promise<void> {
        const newThis = this
        return new Promise(async (resolve, reject) => {
            try {
                const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
                if (account == null) {
                    Logger.logInfo(newThis.fileName, "saveWebData", "Account not found");
                    reject("Account not found");
                    return;
                }
                await window.UnviredDB.saveWebDB({userId: "" + account.getAccountId()});
                resolve();
            }
            catch(e) {
                Logger.logError(newThis.fileName, 'saveWebData', e);
                reject(e);
            }
        });
    }

    private async getFieldMetaDataList(classRef: any): Promise<FieldMeta[]> {
        if (classRef.fieldMetas.length === 0) {
            const fieldMetas = await this.select(DatabaseType.FrameworkDb, "FieldMeta");
            classRef.fieldMetas = (fieldMetas == null || fieldMetas.length === 0) ? [] : fieldMetas;
        }
        return classRef.fieldMetas;
    }

    private async constructInsertOrReplaceQuery(tableName: string, jsonData: any, prefixString: string, isHeader: boolean, isFwTable: boolean, classRef: any, isFromApp: boolean = false): Promise<string> {
        if (jsonData[FieldConstants.FieldLid] == null || jsonData[FieldConstants.FieldLid] === "") {
            jsonData[FieldConstants.FieldLid] = FrameworkHelper.getUUID();
        }

        if (!isHeader && (jsonData[FieldConstants.FieldFid] == null || jsonData[FieldConstants.FieldFid] === "")) {
            Logger.logError("DatabaseManager", "constructInsertOrReplaceQueries",
                `${tableName} FID field is not set.`);
            throw new Error(`${tableName} FID field is not set.`);
        }

        const fieldMetas = await this.getFieldMetaDataList(classRef);

        if (!isFwTable) {
            if (fieldMetas.length === 0) {
                Logger.logError("DatabaseManager", "constructInsertOrReplaceQueries",
                    `${tableName} fields are not found in Framework DB in FieldMeta table.`);
                throw new Error(`${tableName} fields are not found in Framework DB in FieldMeta table.`);
            }

            jsonData = JSON.parse(JSON.stringify(jsonData));
            jsonData[FieldConstants.FieldTimestamp]  = Date.now();

            if (isFromApp) {
                if (jsonData[FieldConstants.FieldObjectStatus] == null) {
                    if (prefixString === "INSERT") {
                        jsonData[FieldConstants.FieldObjectStatus] = ObjectStatus.add;
                    } else {
                        jsonData[FieldConstants.FieldObjectStatus] = ObjectStatus.modify;
                    }
                }
                if (jsonData[FieldConstants.FieldSyncStatus] == null) {
                    jsonData[FieldConstants.FieldSyncStatus] = SyncStatus.none;
                }
            }
        }

        const entityFieldMetas = fieldMetas.filter(element => element.structureName === tableName);

        if (tableName.endsWith(AttachmentBE)) {
            jsonData = await this.copyAttachmentToApplicationFolder(jsonData);
        }
        let fieldsString = "";
        let valuesString = "";
        const jsonDataKeys = Object.keys(jsonData);
        for (const key of jsonDataKeys) {
            let value = jsonData[key];
            if (!isFwTable && entityFieldMetas.findIndex(element => element.fieldName === key) === -1 && !constantFields.includes(key)) {
                continue;
            }
            if (isFwTable) {
                value = value == null ? "NULL" : value;
            }
            if (value !== null && value != undefined) {
                if (typeof value === 'string') {
                    value = this.escapeSqlString(value);
                    value = `'${value}'`;
                }
                fieldsString += (fieldsString.length === 0 ? "" : ", ") + key;
                valuesString += (valuesString.length === 0 ? "" : ", ") + value;
            }
        }
        const queryString = `${prefixString} INTO ${tableName} (${fieldsString}) VALUES (${valuesString})`;
        return queryString;
    }

    private async constructUpdateQuery(tableName: string, jsonData: any, whereClause: string, isFwTable: boolean, classRef: any, isFromApp: boolean = false): Promise<string> {

        const fieldMetas = await this.getFieldMetaDataList(classRef);

        if (!isFwTable && fieldMetas.length === 0) {
            Logger.logError("DatabaseManager", "constructInsertOrReplaceQueries",
                `${tableName} fields are not found in Framework DB in FieldMeta table.`);
            throw new Error(`${tableName} fields are not found in Framework DB in FieldMeta table.`);
        }

        const entityFieldMetas = fieldMetas.filter(element => element.structureName === tableName);
        if (!isFwTable && entityFieldMetas.length === 0) {
            Logger.logError("DatabaseManager", "constructInsertOrReplaceQueries",
                `${tableName} fields are not found for structure name: ${tableName}.`);
            throw new Error(`${tableName} fields are not found for structure name: ${tableName}.`);
        }

        jsonData = JSON.parse(JSON.stringify(jsonData));

        if (!isFwTable) {
            jsonData[FieldConstants.FieldTimestamp]  = Date.now();
        }

        if (!isFwTable && isFromApp) {
            jsonData[FieldConstants.FieldObjectStatus] = ObjectStatus.modify;
        }
        if (tableName.endsWith(AttachmentBE)) {
            jsonData = await this.copyAttachmentToApplicationFolder(jsonData);
        }
        let updateString = "";
        const jsonDataKeys = Object.keys(jsonData);
        for (const key of jsonDataKeys) {
            let value = jsonData[key];
            if (!isFwTable && entityFieldMetas.findIndex(element => element.fieldName === key) === -1 && !constantFields.includes(key)) {
                continue;
            }
            if (value !== null && value != undefined) {
                if (typeof value === 'string') {
                    value = this.escapeSqlString(value);
                    value = `'${value}'`;
                }
                updateString += (updateString.length == 0 ? "" : ", ") + `${key}=${value}`;
            }
            else {
                updateString += (updateString.length == 0 ? "" : ", ") + `${key}=NULL`;
            }
        }
        if (!isFwTable && !isFromApp) {
            // Add missing fields with null values
            for (const fieldMeta of entityFieldMetas) {
                if (!jsonDataKeys.includes(fieldMeta.fieldName) &&
                    !constantFields.includes(fieldMeta.fieldName) &&
                    fieldMeta.fieldName !== FieldConstants.FieldLid &&
                    fieldMeta.fieldName !== FieldConstants.FieldFid) {
                    updateString += (updateString.length === 0 ? "" : ", ") +
                        `${fieldMeta.fieldName}=NULL`;
                }
            }
        }
        var queryString = `UPDATE ${tableName} SET ${updateString}`;
        if (whereClause != null && whereClause != "") {
            queryString += ` WHERE ${whereClause}`
        }
        return queryString;
    }

    private async copyAttachmentToApplicationFolder(attachmentItem: any): Promise<any> {
        if (attachmentItem[ServiceConstants.AttachmentItemFieldLocalPath] == null) {
          await Logger.logInfo("Attachment", "copyAttachmentToApplicationFolder",
            `${ServiceConstants.AttachmentItemFieldLocalPath} is null. Nothing to copy.`);
          return attachmentItem;
        }
      
        const localPath: string = attachmentItem[ServiceConstants.AttachmentItemFieldLocalPath];
        const uid: string = FrameworkHelper.getUUID();
      
        // if fileName is missing, then Use the UID for the fileName and No Extension
        let fileName: string = "";
        if (attachmentItem[ServiceConstants.AttachmentItemFieldFileName] != null) {
          fileName = attachmentItem[ServiceConstants.AttachmentItemFieldFileName];
        } else if (attachmentItem[ServiceConstants.AttachmentItemFieldLocalPath] != null) {
          let tempLocalPath: string = localPath;
          if (tempLocalPath.endsWith('/')) {
            tempLocalPath = tempLocalPath.substring(0, tempLocalPath.length - 1);
          }
          const pathArray: string[] = tempLocalPath.split("/");
          if (pathArray.length > 0) {
            fileName = pathArray[pathArray.length - 1];
          } else {
            fileName = uid;
          }
        } else {
          fileName = uid;
        }
      
        try {
            const arrayBuffer = await AttachmentHelper.readFileAsArrayBuffer(localPath);
            if (arrayBuffer == null) {
                Logger.logError("Attachment", "copyAttachmentToApplicationFolder",
                    `Unable to read attachment from ${localPath}`);
                return attachmentItem;
            }
            const attachmentPath = await AttachmentHelper.addAttachment(fileName, arrayBuffer);
            attachmentItem[ServiceConstants.AttachmentItemFieldLocalPath] = attachmentPath;
        } catch (e) {
          await Logger.logInfo("Attachment", "copyAttachmentToApplicationFolder",
            `Unable to copy attachment from ${localPath} to application folder.`);
        }
        return attachmentItem;
    }

    public escapeSqlString(value: string): string {
        if (value === null || value === undefined || typeof value !== 'string') {
            return value;
        }
        const regex = /'/g;
        return value.replace(regex, "''");
    }
}

export default DatabaseManager;