import { FieldMeta, StructureMeta } from "../applicationMeta/applicationMetadataParser";
import * as FieldConstants from "../applicationMeta/fieldConstants";
import AttachmentQHelper from "../attachment/attachmentQHelper";
import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import FrameworkHelper from "./frameworkHelper";
import ServerResponseHandler from "./serverResponseHandler";
import * as ServiceConstants from './serviceConstants';
import SyncInputDataManager, { DataType } from "./syncInputDataManager";
import { ObjectStatus, SyncStatus } from "./utils";

declare var Logger: any;

export enum ReconcilerType {
    RequestResponse,
    PullPushQuery
}

class Reconciler {
    private entityName: string = "";
    private entityInDB: any = {};
    private incomingEntity: any = {};
    private incomingItems: any = {};
    private conflictRule: string = "";
    private isForeground: boolean = true;

    private incomingItemLids: any = {};
    private incomingAttachmentItemLids: any = {};

    private conflictBe: any = {};

    private databaseManager: DatabaseManager = DatabaseManager.getInstance();

    private reconcilerType: ReconcilerType = ReconcilerType.RequestResponse;

    constructor(
        entityName: string,
        entityInDB: any,
        incomingEntity: any,
        incomingItems: any,
        conflictRule: string,
        isForeground: boolean,
        reconcilerType: ReconcilerType
    ) {
        this.entityName = entityName;
        this.entityInDB = entityInDB;
        this.incomingEntity = incomingEntity;
        this.incomingItems = incomingItems;
        this.conflictRule = conflictRule;
        this.isForeground = isForeground;
        this.reconcilerType = reconcilerType;
    }

    public getConflictBe(): any {
        return this.conflictBe;
    }

    public async reconcile(structureMetas: StructureMeta[], fieldMetas: FieldMeta[], requestType: ServiceConstants.RequestType, lid: string): Promise<boolean> {
        // Conflict Management.
        // Check whether |currentIncomingHeader| is conflicted.
        if (
            this.incomingEntity[FieldConstants.FieldConflict]?.toString().toUpperCase() === "X"
        ) {
            const headerMeta = structureMetas.find(
                (element) => element.structureName === this.entityName
            );
            Logger.logInfo(
                "Reconciler",
                "reconcile",
                `Header with BE: ${headerMeta?.beName}, LID: ${this.entityInDB[FieldConstants.FieldLid]} is in Conflict. Sending Header to Conflict Manager.`
            );
            const conflictStatus = await this.manageConflict(
                headerMeta,
                this.entityInDB,
                this.incomingEntity,
                this.incomingItems
            );
            return conflictStatus;
        }

        let status = true;
        const objectStatus = this.entityInDB[FieldConstants.FieldObjectStatus] as number;
        switch (objectStatus) {
            case ObjectStatus.global:
                status = await this.actionHeaderForGlobal();
                break;

            case ObjectStatus.add:
                status = await this.actionHeaderForAdd();
                break;

            case ObjectStatus.modify:
                status = await this.actionHeaderForModify();
                break;

            case ObjectStatus.delete:
                status = await this.actionHeaderForDelete();
                break;

            default:
                break;
        }

        if (!status) {
            return false;
        }

        // Handle Items
        const keys = Object.keys(this.incomingItems);
        for (const key of keys) {
            const itemsArray = this.incomingItems[key] as any[];
            for (const item of itemsArray) {
                const itemInDB = await ServerResponseHandler.checkDuplicateBe(
                    key,
                    fieldMetas,
                    item,
                    requestType,
                    lid
                );
                if (key.endsWith(ServiceConstants.AttachmentBE)) {
                    // Handle Attachment Items
                    await this.handleAttachmentItems(key, itemInDB, item);
                } else {
                    // Handle items without Attachment
                    await this.handleItems(key, itemInDB, item);
                }
            }
        }

        await this.handleDeletionOfItemsInDBWithoutIncomingItems(structureMetas);
        await this.handleDeletionOfAttachmentItemsInDBWithoutIncomingItems(
            structureMetas
        );
        return true;
    }

    private setHeaderFieldsAndStatuses() {
        this.incomingEntity[FieldConstants.FieldLid] = this.entityInDB[FieldConstants.FieldLid];
        this.incomingEntity[FieldConstants.FieldObjectStatus] = ObjectStatus.global;
        this.incomingEntity[FieldConstants.FieldSyncStatus] = SyncStatus.none;
        this.incomingEntity[FieldConstants.FieldInfoMsgCat] = '';
        this.incomingEntity[FieldConstants.FieldTimestamp] = this.entityInDB[FieldConstants.FieldTimestamp];
    }
    
    private async insertDataIntoDB(tableName: string, dataToInsert: any, isHeader:boolean): Promise<boolean> {
        this.setHeaderFieldsAndStatuses();
        try {
            await this.databaseManager.insert(DatabaseType.AppDb, tableName, dataToInsert, isHeader);
            return true;
        }
        catch (e) {
            Logger.logDebug("Reconciler", "insertDataIntoDB", `Error while inserting the data into database. Table Name: ${tableName}. Input Data: ${dataToInsert.toString()}. Error: ${JSON.stringify(e)}`);
            Logger.logError("Reconciler", "insertDataIntoDB", "Error while inserting the data into database. " + JSON.stringify(e));
            return false;
        }
    }

    private async updateDataIntoDB(tableName: string, dataToUpdate: any, whereClaues:string): Promise<boolean> {
        this.setHeaderFieldsAndStatuses();
        try {
            await this.databaseManager.update(DatabaseType.AppDb, tableName, dataToUpdate, whereClaues);
            return true;
        }
        catch (e) {
            Logger.logDebug("Reconciler", "updateDataIntoDB", `Error while updating the data into database. Table Name: ${tableName}. Input Data: ${JSON.stringify(dataToUpdate)}. Error: ${JSON.stringify(e)}`);
            Logger.logError("Reconciler", "updateDataIntoDB", "Error while updating the data into database. " + JSON.stringify(e));
            return false;
        }
    }

    private async actionHeaderForGlobal(): Promise<boolean> {
        if (this.isForeground && this.reconcilerType == ReconcilerType.RequestResponse) {
            return await this.updateDataIntoDB(this.entityName, this.incomingEntity, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`)
        }
        const syncStatus = this.entityInDB[FieldConstants.FieldSyncStatus] as number;
        switch (syncStatus) {
            case SyncStatus.none: {
                return await this.updateDataIntoDB(this.entityName, this.incomingEntity, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`);
            }
            case SyncStatus.queued:
            case SyncStatus.sent:
            case SyncStatus.error: {
                Logger.logError(
                    "PullPushQueryReconciler",
                    "actionHeaderForGlobal",
                    `Reconciler Type: ${ReconcilerType[this.reconcilerType]}. Invalid case. Header in Object Status GLOBAL can only be in Sync Status NONE. Current Sync Status: ${syncStatus}, Header Information: ${this.entityName}. LID: ${this.entityInDB[FieldConstants.FieldLid]}`
                );
                return false;
            }
            default:
                break;
        }
        return true;
    }

    private async actionHeaderForAdd(): Promise<boolean> {
        if (this.isForeground && this.reconcilerType == ReconcilerType.RequestResponse) {
            return await this.updateDataIntoDB(this.entityName, this.incomingEntity, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`)
        }
        const syncStatus = this.entityInDB[FieldConstants.FieldSyncStatus] as number;
        switch (syncStatus) {
            case SyncStatus.none:
            case SyncStatus.queued:
            case SyncStatus.sent:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    return await this.updateDataIntoDB(this.entityName, this.incomingEntity, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`)
                }
            case SyncStatus.error: {
                var logMessage = `Reconciler Type: ${ReconcilerType[this.reconcilerType]}. Ignoring the locally added DataStructure: ${this.entityName}.`
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    logMessage = `Reconciler Type: ${ReconcilerType[this.reconcilerType]}. Invalid case. Header in Object Status ADD can only be in Sync Status SENT. Current Sync Status: ${syncStatus}, Header Information: ${this.entityName}. LID: ${this.entityInDB[FieldConstants.FieldLid]}`;
                }
                Logger.logInfo("PullPushQueryReconciler", "actionHeaderForAdd", logMessage);
                return true;
            }
            default:
                break;
        }
        return false;
    }

    private async actionHeaderForModify(): Promise<boolean> {
        if (this.isForeground && this.reconcilerType == ReconcilerType.RequestResponse) {
            return await this.updateDataIntoDB(this.entityName, this.incomingEntity, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`)
        }
        const syncStatus = this.entityInDB[FieldConstants.FieldSyncStatus] as number;
        switch (syncStatus) {
            case SyncStatus.none:
                return await this.handleHeaderConflict();
            case SyncStatus.sent: {
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    return await this.updateDataIntoDB(this.entityName, this.incomingEntity, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`)
                }
                Logger.logInfo(
                    "PullPushQueryReconciler",
                    "actionHeaderForModify",
                    `Ignoring the locally added DataStructure: ${this.entityName}.`
                );
                return true;
            }
            case SyncStatus.queued:
            case SyncStatus.error:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logError(
                        "RequestResponseReconciler",
                        "actionHeaderForModify",
                        `Invalid case. Header in Object Status MODIFY can only be in Sync Status SENT. Current Sync Status: ${syncStatus}, Header Information: ${this.entityName}. LID: ${this.entityInDB[FieldConstants.FieldLid]}`
                    );
                    return false;
                }
                else {
                    return await this.handleHeaderConflict();
                }
            default:
                break;
        }
        return false;
    }

    private async actionHeaderForDelete(): Promise<boolean> {
        // If the Execution type is Foreground then we should not check for the SyncStatus. Because SYNC Statuses typically do not change for a SYNC Call.
        if (this.isForeground && this.reconcilerType == ReconcilerType.RequestResponse) {
            Logger.logInfo("RequestResponseReconciler", "actionHeaderForDelete", `Deleting The Header in Database. Table: ${this.entityName}`);
            // Delete the Current Header in Database.
            const result = await this.databaseManager.delete(DatabaseType.AppDb, this.entityName, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`);
            return result !== 0;
        }
        const syncStatus = this.entityInDB[FieldConstants.FieldSyncStatus] as number;
        switch (syncStatus) {
            case SyncStatus.none:
            case SyncStatus.queued:
            case SyncStatus.error:
                Logger.logError(
                    "PullPushQueryReconciler",
                    "actionHeaderForDelete",
                    `Invalid case. Header in Object Status DELETE can only be in Sync Status SENT. Current Sync Status: ${syncStatus}, Header Information: ${this.entityName}. LID: ${this.entityInDB[FieldConstants.FieldLid]}`
                );
                return false;
            case SyncStatus.sent: {
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logInfo("RequestResponseReconciler", "actionHeaderForDelete", `Deleting The Header in Database. Table: ${this.entityName}`);
                    // Delete the Current Header in Database.
                    const result = await this.databaseManager.delete(DatabaseType.AppDb, this.entityName, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`);
                    return result !== 0;
                }
                return await this.updateDataIntoDB(this.entityName, this.incomingEntity, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`);
            }
            default:
                break;
        }
        return false;
    }

    private async handleHeaderConflict(): Promise<boolean> {
        switch (this.conflictRule) {
            case FieldConstants.ConflictModeServerWins:
                // modify the header and set statuses
                return await this.updateDataIntoDB(this.entityName, this.incomingEntity, `${FieldConstants.FieldLid}='${this.entityInDB[FieldConstants.FieldLid]}'`);

            case FieldConstants.ConflictModeDeviceWins:
                // Do not touch the header. Let the header data in the device remain. Do not do anything here.
                break;

            case FieldConstants.ConflictModeAppHandled:
                // Provide the conflict object back so that the application can handle it
                // TODO:
                // if (this.conflictBe == null) {
                //     this.conflictBe = new ConflictBE(this.currentDBHeader, this.currentIncomingHeader);
                // }
                // this.conflictBe.isHeaderInConflict = true;
                break;
        }
        return true;
    }

    private async handleItems(itemName: string, itemInDB: any, incomingItem: any): Promise<void> {
        if (itemInDB == null) {
            incomingItem[FieldConstants.FieldLid] = FrameworkHelper.getUUID();
            incomingItem[FieldConstants.FieldFid] = this.entityInDB[FieldConstants.FieldLid];
            incomingItem[FieldConstants.FieldObjectStatus] = ObjectStatus.global;
            incomingItem[FieldConstants.FieldSyncStatus] = SyncStatus.none;

            const isDatabaseOperationSuccessfull = await this.insertDataIntoDB(itemName, incomingItem, false);
            if (!isDatabaseOperationSuccessfull) {
                return;
            }
        } else {
            const objectStatus = itemInDB[FieldConstants.FieldObjectStatus];
            switch (objectStatus) {
                case ObjectStatus.global:
                    await this.actionOnIncomingItemForGlobal(itemName, itemInDB, incomingItem);
                    break;

                case ObjectStatus.add:
                    await this.actionOnIncomingItemForAdd(itemName, itemInDB, incomingItem);
                    break;

                case ObjectStatus.modify:
                    await this.actionOnIncomingItemForModify(itemName, itemInDB, incomingItem);
                    break;

                case ObjectStatus.delete:
                    await this.actionOnIncomingItemForDelete(itemName, itemInDB, incomingItem);
                    break;

                default:
                    break;
            }
        }
        this.addItemToCollection(itemName, incomingItem);
    }

    private async actionOnIncomingItemForGlobal(itemName: string, itemInDB: any, incomingItem: any): Promise<boolean> {
        const syncStatus: number = itemInDB[FieldConstants.FieldSyncStatus];
        switch (syncStatus) {
            case SyncStatus.none:
                // Instead of copying all fields from incomingItemDataStructure to itemDataStructureInDB, copying the LID from itemDataStructureInDB into incomingItemDataStructure
                const item: any = this.setItemFieldsAndStatusesFor(itemInDB, incomingItem);
                return await this.updateDataIntoDB(itemName, item, `${FieldConstants.FieldLid}='${incomingItem[FieldConstants.FieldLid]}'`);
            case SyncStatus.error:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const item: any = this.setItemFieldsAndStatusesFor(itemInDB, incomingItem);
                    return await this.updateDataIntoDB(itemName, item, `${FieldConstants.FieldLid}='${incomingItem[FieldConstants.FieldLid]}'`);
                }
            case SyncStatus.queued:
            case SyncStatus.sent:
                Logger.logError("PullPushQueryReconciler", "actionOnIncomingItemForGlobal", `Invalid case. Items in Object Status GLOBAL can only be in Sync Status NONE. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`);
                break;
            default:
                break;
        }
        return false;
    }

    private async actionOnIncomingItemForAdd(itemName: string, itemInDB: any, incomingItem: any): Promise<boolean> {
        if (this.isForeground && this.reconcilerType == ReconcilerType.RequestResponse) {
            const item: any = this.setItemFieldsAndStatusesFor(itemInDB, incomingItem);
            return await this.updateDataIntoDB(itemName, item, `${FieldConstants.FieldLid}='${incomingItem[FieldConstants.FieldLid]}'`);
        }

        const syncStatus: number = itemInDB[FieldConstants.FieldSyncStatus];
        switch (syncStatus) {
            case SyncStatus.none:
            case SyncStatus.queued:
            case SyncStatus.error:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    // Delete Item. Not possible to relate an ADD status to incoming item
                    const result = await this.databaseManager.delete(DatabaseType.AppDb, itemName, `${FieldConstants.FieldLid}='${itemInDB[FieldConstants.FieldLid]}'`);
                    return result !== 0;
                }
            case SyncStatus.sent:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const item: any = this.setItemFieldsAndStatusesFor(itemInDB, incomingItem);
                    return await this.databaseManager.insertOrUpdate(DatabaseType.AppDb, itemName, item, false);
                }
                Logger.logInfo("PullPushQueryReconciler", "actionOnIncomingItemForModify", `Do not touch. Has to be handled by request-response. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`);
                break;
            default:
                break;
        }
        return true;
    }

    private async actionOnIncomingItemForModify(itemName: string, itemInDB: any, incomingItem: any): Promise<boolean> {
        if (this.isForeground && this.reconcilerType == ReconcilerType.RequestResponse) {
            const item: any = this.setItemFieldsAndStatusesFor(itemInDB, incomingItem);
            return await this.updateDataIntoDB(itemName, item, `${FieldConstants.FieldLid}='${incomingItem[FieldConstants.FieldLid]}'`);
        }
        const syncStatus: number = itemInDB[FieldConstants.FieldSyncStatus];
        switch (syncStatus) {
            case SyncStatus.none:
                return this.handleItemConflictWithItemDataStructure(itemName, itemInDB, incomingItem);
            case SyncStatus.sent:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const item: any = this.setItemFieldsAndStatusesFor(itemInDB, incomingItem);
                    return await this.databaseManager.insertOrUpdate(DatabaseType.AppDb, itemName, item, false);
                }
                Logger.logInfo("PullPushQueryReconciler", "actionOnIncomingItemForModify", `Server did not return this item which is modified on the client and with the Sync Status as SENT. Reconciler retaining this item. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`);
                break;
            case SyncStatus.queued:
            case SyncStatus.error:
                if (this.reconcilerType == ReconcilerType.PullPushQuery) {
                    return this.handleItemConflictWithItemDataStructure(itemName, itemInDB, incomingItem);
                }
                Logger.logError(
                    "RequestResponseReconciler",
                    "actionOnIncomingItemForModify",
                    `Invalid case. Items in Object Status MODIFY can only be in Sync Status SENT. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`);
                
            default:
                break;
        }
        return false;
    }

    private async actionOnIncomingItemForDelete(itemName: string, itemInDB: any, incomingItem: any): Promise<boolean> {
        const syncStatus: number = itemInDB[FieldConstants.FieldSyncStatus];
        switch (syncStatus) {
            case SyncStatus.none:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logError(
                        "RequestResponseReconciler",
                        "actionOnIncomingItemForDelete",
                        `Do not touch. Item has been modified after synchronization before getting a response. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`);
                    return true;
                }
            case SyncStatus.queued:
            case SyncStatus.error:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logError(
                        "RequestResponseReconciler",
                        "actionOnIncomingItemForDelete",
                        `Invalid case. Items in Object Status DELETE can only be in Sync Status SENT. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`);
                    return false;
                }
                return this.handleItemConflictWithItemDataStructure(itemName, itemInDB, incomingItem);
            case SyncStatus.sent:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const item: any = this.setItemFieldsAndStatusesFor(itemInDB, incomingItem);
                    return await this.updateDataIntoDB(itemName, item, `${FieldConstants.FieldLid}='${incomingItem[FieldConstants.FieldLid]}'`);
                }
                Logger.logInfo("PullPushQueryReconciler", "actionOnIncomingItemForDelete", `Server did not return this item which is marked for DELETE on the client and with the Sync Status as SENT. Reconciler retaining this item. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`);
                break;
            default:
                break;
        }
        return false;
    }

    private async handleItemConflictWithItemDataStructure(itemName: string, itemInDB: any, incomingItem: any): Promise<boolean> {
        switch (this.conflictRule) {
            case FieldConstants.ConflictModeServerWins: {
                // modify the item and set statuses
                const item = this.setItemFieldsAndStatusesFor(itemInDB, incomingItem);
                return await this.updateDataIntoDB(itemName, item, `${FieldConstants.FieldLid}='${incomingItem[FieldConstants.FieldLid]}'`);
            }

            case FieldConstants.ConflictModeDeviceWins:
                // Do not touch the item. Let the item data in the device remain. Do not do anything here.
                break;

            case FieldConstants.ConflictModeAppHandled: {
                // Provide the conflict object back so that the application can handle it

                // TODO:
                // if (this.conflictBe == null) {
                //     this.conflictBe = new ConflictBE(this.currentDBHeader, this.currentIncomingHeader);
                // }

                // this.conflictBe.setItemInConflict(itemDataStructureInDB, incomingItemDataStructure);
            }
            break;
        }
        return true;
    }

    private async handleAttachmentItems(attachmentItemName: string, attachmentItemInDB: any, incomingAttachmentItem: any): Promise<void> {
        // If attachmentItem is not in Db..then insert and add to collection else resolve the conflict..
        if (attachmentItemInDB === null) {
            incomingAttachmentItem[FieldConstants.FieldLid] = FrameworkHelper.getUUID();
            incomingAttachmentItem[FieldConstants.FieldFid] = this.entityInDB[FieldConstants.FieldLid];
            incomingAttachmentItem[FieldConstants.FieldObjectStatus] = ObjectStatus.global;
            incomingAttachmentItem[FieldConstants.FieldSyncStatus] = SyncStatus.none;
            try {
                await this.insertDataIntoDB(attachmentItemName, incomingAttachmentItem, false);
                AttachmentQHelper.checkAttachmentAndQueueForAutoDownload(attachmentItemName, incomingAttachmentItem);
            } catch (e) {
                Logger.logDebug("RequestResponseReconciler", "handleAttachmentItems", "Error while inserting attachment. Input data: " + incomingAttachmentItem.toString());
                Logger.logError("RequestResponseReconciler", "handleAttachmentItems", "Error while inserting attachment. Error: " + JSON.stringify(e));
            }
        } else {
            try {
                await this.databaseManager.delete(DatabaseType.FrameworkDb, "InfoMessage", `belid='${attachmentItemInDB[FieldConstants.FieldLid]}'`);
            } catch (e) {
                Logger.logError("PullPushQueryReconciler", "handleAttachmentItems", "Error while deleting info message. Error: " + JSON.stringify(e));
            }
            const objectStatus = attachmentItemInDB[FieldConstants.FieldObjectStatus];
            switch (objectStatus) {
                case ObjectStatus.global:
                    await this.actionOnIncomingAttachmentItemForGlobal(attachmentItemName, attachmentItemInDB, incomingAttachmentItem);
                    break;

                case ObjectStatus.add:
                    await this.actionOnIncomingAttachmentItemForAdd(attachmentItemName, attachmentItemInDB, incomingAttachmentItem);
                    break;

                case ObjectStatus.modify:
                    await this.actionOnIncomingAttachmentItemForModify(attachmentItemName, attachmentItemInDB, incomingAttachmentItem);
                    break;

                case ObjectStatus.delete:
                    await this.actionOnIncomingAttachmentItemForDelete(attachmentItemName, attachmentItemInDB, incomingAttachmentItem);
                    break;

                default:
                    break;
            }
        }
        this.addAttachmentItemToCollection(attachmentItemName, incomingAttachmentItem);
    }

    private async actionOnIncomingAttachmentItemForGlobal(itemName: string, attachmentItemInDB: any, incomingAttachmentItem: any): Promise<boolean> {
        const syncStatus: number = attachmentItemInDB[FieldConstants.FieldSyncStatus];
        switch (syncStatus) {
            case SyncStatus.none:
                const attachmentItemToUpdate: any = this.setAttachmentItemFieldsAndStatuses(attachmentItemInDB, incomingAttachmentItem);
                return await this.updateDataIntoDB(itemName, attachmentItemToUpdate, `${FieldConstants.FieldLid}='${incomingAttachmentItem[FieldConstants.FieldLid]}'`)
            case SyncStatus.queued:
            case SyncStatus.sent:
            case SyncStatus.error:
                Logger.logError("PullPushQueryReconciler", "actionOnIncomingAttachmentItemForGlobal", `Invalid case. Items in this status should have OBJECT_STATUS as ADD, MODIFY or DELETE. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`);
                return false;
            default:
                break;
        }
        return false;
    }
 
    private async actionOnIncomingAttachmentItemForAdd(itemName: string, attachmentItemInDB: any, incomingAttachmentItem: any): Promise<boolean> {
        if (this.isForeground && this.reconcilerType == ReconcilerType.RequestResponse) {
            const attachmentItemToUpdate: any = this.setAttachmentItemFieldsAndStatuses(attachmentItemInDB, incomingAttachmentItem);
            return await this.updateDataIntoDB(itemName, attachmentItemToUpdate, `${FieldConstants.FieldLid}='${incomingAttachmentItem[FieldConstants.FieldLid]}'`)
        }
        const syncStatus: number = attachmentItemInDB[FieldConstants.FieldSyncStatus];
        switch (syncStatus) {
            case SyncStatus.none:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logError("PullPushQueryReconciler", "actionOnIncomingAttachmentItemForAdd", `Do not touch. Item has been modified after synchronization before getting a response. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`);
                    return false;
                }
            case SyncStatus.sent:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const attachmentItemToUpdate: any = this.setAttachmentItemFieldsAndStatuses(attachmentItemInDB, incomingAttachmentItem);
                    return await this.updateDataIntoDB(itemName, attachmentItemToUpdate, `${FieldConstants.FieldLid}='${incomingAttachmentItem[FieldConstants.FieldLid]}'`)
                }
            case SyncStatus.queued:
            case SyncStatus.error:
                Logger.logError("PullPushQueryReconciler", "actionOnIncomingAttachmentItemForAdd", `Do not touch. Has to be handled by the Attachment upload queue separately. For ERROR application has to resolve. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`);
                return false;
            default:
                break;
        }
        return false;
    }

    private async actionOnIncomingAttachmentItemForModify(itemName: string, attachmentItemInDB: any, incomingAttachmentItem: any): Promise<boolean> {
        if (this.isForeground && this.reconcilerType == ReconcilerType.RequestResponse) {
            const attachmentItemToUpdate: any = this.setAttachmentItemFieldsAndStatuses(attachmentItemInDB, incomingAttachmentItem);
            return await this.updateDataIntoDB(itemName, attachmentItemToUpdate, `${FieldConstants.FieldLid}='${incomingAttachmentItem[FieldConstants.FieldLid]}'`)
        }
        const syncStatus: number = attachmentItemInDB[FieldConstants.FieldSyncStatus];
        switch (syncStatus) {
            case SyncStatus.none:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logError("PullPushQueryReconciler", "actionOnIncomingAttachmentItemForModify", `Do not touch. Has to be handled by the Attachment upload queue separately. For ERROR application has to resolve. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`);
                    return true;
                }
            case SyncStatus.sent:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const attachmentItemToUpdate: any = this.setAttachmentItemFieldsAndStatuses(attachmentItemInDB, incomingAttachmentItem);
                    return await this.updateDataIntoDB(itemName, attachmentItemToUpdate, `${FieldConstants.FieldLid}='${incomingAttachmentItem[FieldConstants.FieldLid]}'`)
                }
            case SyncStatus.queued:
            case SyncStatus.error:
                // Invalid case. Items in Object Status MODIFY can only be in Sync Status SENT.
                Logger.logError("PullPushQueryReconciler", "actionOnIncomingAttachmentItemForModify",`Do not touch. Has to be handled by the Attachment upload queue separately. For ERROR application has to resolve. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`);
                return false;
            default:
                break;
        }
        return false;
    }

    private async actionOnIncomingAttachmentItemForDelete(itemName: string, attachmentItemInDB: any, incomingAttachmentItem: any): Promise<boolean> {
        const syncStatus: number = attachmentItemInDB[FieldConstants.FieldSyncStatus];
        switch (syncStatus) {
            case SyncStatus.none:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logError("PullPushQueryReconciler", "actionOnIncomingAttachmentItemForDelete", `Do not touch. Has to be handled by the Attachment upload queue separately. For ERROR application has to resolve. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`);
                    return true;
                }
            case SyncStatus.sent:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const attachmentItemToUpdate: any = this.setAttachmentItemFieldsAndStatuses(attachmentItemInDB, incomingAttachmentItem);
                    return await this.updateDataIntoDB(itemName, attachmentItemToUpdate, `${FieldConstants.FieldLid}='${incomingAttachmentItem[FieldConstants.FieldLid]}'`)
                }
            case SyncStatus.queued:
            case SyncStatus.error:
                // Invalid case. Items in Object Status MODIFY can only be in Sync Status SENT.
                Logger.logError("PullPushQueryReconciler", "actionOnIncomingAttachmentItemForDelete", `Do not touch. Has to be handled by the Attachment upload queue separately. For ERROR application has to resolve. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`);
                return false;
            default:
                break;
        }
        return false;
    }

    private addItemToCollection(itemName: string, item: any): void {
        if (item == null) {
            return;
        }
        let items: string[] | undefined = this.incomingItemLids[itemName];
        if (items == null) {
            items = [];
        }
        items.push(item[FieldConstants.FieldLid]);
        this.incomingItemLids[itemName] = items;
    }

    private addAttachmentItemToCollection(attachmentItemName: string, attachmentItem: any): void {
        if (attachmentItem == null) {
            return;
        }
        let items: string[] | undefined = this.incomingAttachmentItemLids[attachmentItemName];
        if (items == null) {
            items = [];
        }
        items.push(attachmentItem[FieldConstants.FieldLid]);
        this.incomingAttachmentItemLids[attachmentItemName] = items;
    }

    private setItemFieldsAndStatusesFor(itemInDB: any, incomingItem: any): any {
        // Setting the LID of the incoming Item to the Item present in DB so that its easier for updation.
        incomingItem[FieldConstants.FieldLid] = itemInDB[FieldConstants.FieldLid];
        incomingItem[FieldConstants.FieldFid] = this.incomingEntity[FieldConstants.FieldLid];
        incomingItem[FieldConstants.FieldObjectStatus] = ObjectStatus.global;
        incomingItem[FieldConstants.FieldSyncStatus] = SyncStatus.none;
        incomingItem[FieldConstants.FieldTimestamp] = itemInDB[FieldConstants.FieldTimestamp]; //TODO : CHeck if timestamp needs to be updated
        return incomingItem;
    }

    private setAttachmentItemFieldsAndStatuses(attachmentItemInDB: any, incomingAttachmentItem: any): any {
        incomingAttachmentItem[FieldConstants.FieldLid] = attachmentItemInDB[FieldConstants.FieldLid];
        incomingAttachmentItem[FieldConstants.FieldFid] = this.incomingEntity[FieldConstants.FieldLid];
        incomingAttachmentItem[FieldConstants.FieldObjectStatus] = ObjectStatus.global;
        incomingAttachmentItem[FieldConstants.FieldSyncStatus] = SyncStatus.none;
        incomingAttachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = attachmentItemInDB[ServiceConstants.AttachmentItemFieldAttachmentStatus];
        incomingAttachmentItem[ServiceConstants.AttachmentItemFieldFileName] = attachmentItemInDB[ServiceConstants.AttachmentItemFieldFileName];
        incomingAttachmentItem[ServiceConstants.AttachmentItemFieldLocalPath] = attachmentItemInDB[ServiceConstants.AttachmentItemFieldLocalPath];
        return incomingAttachmentItem;
    }

    private async handleDeletionOfItemsInDBWithoutIncomingItems(structureMetas: StructureMeta[]): Promise<void> {
        // Handle Items in DB that do not have a matching incoming item from server

        // Get All the Children Table Names which are not attachmenrts.
        const childrenTableNames: string[] = structureMetas
            .filter((element) => (!element.structureName.endsWith(ServiceConstants.AttachmentBE) && element.isHeader === "0"))
            .map((e) => e.structureName);

        for (const childTableName of childrenTableNames) {
            // Get all items from the child item table that does not have incoming items
            const incomingItemLids: string[] | undefined = this.incomingItemLids[childTableName];
            let whereClauseForExcludingIncomingItems: string = `${FieldConstants.FieldFid} = '${this.entityInDB[FieldConstants.FieldLid]}'`;
            if (incomingItemLids != null && incomingItemLids.length > 0) {
                whereClauseForExcludingIncomingItems += ` AND ${FieldConstants.FieldLid} NOT IN (`;
                for (let i = 0; i < incomingItemLids.length; i++) {
                    if (i !== 0) {
                        whereClauseForExcludingIncomingItems += ",";
                    }
                    whereClauseForExcludingIncomingItems += `'${incomingItemLids[i]}'`;
                }
                whereClauseForExcludingIncomingItems += ")";
            }
            const itemsInDBWithoutIncomingItems: any[] = await this.databaseManager.select(DatabaseType.AppDb, childTableName, whereClauseForExcludingIncomingItems);
            if (itemsInDBWithoutIncomingItems.length > 0) {
                await this.actionOnItemsInDBWithoutIncomingItems(childTableName, itemsInDBWithoutIncomingItems);
            }
        }
    }

    private async actionOnItemsInDBWithoutIncomingItems(itemName: string, itemsInDBWithoutIncomingItems: any[]): Promise<boolean> {
        for (const itemInDB of itemsInDBWithoutIncomingItems) {
            const objectStatus: number = itemInDB[FieldConstants.FieldObjectStatus];
            switch (objectStatus) {
                case ObjectStatus.global:
                    await this.actionOnDBItemWithoutIncomingItemForGlobal(itemName, itemInDB);
                    break;

                case ObjectStatus.add:
                    await this.actionOnDBItemWithoutIncomingItemForAdd(itemName, itemInDB);
                    break;

                case ObjectStatus.modify:
                case ObjectStatus.delete:
                    await this.actionOnDBItemWithoutIncomingItemForModifyOrDelete(itemName, itemInDB);
                    break;

                default:
                    break;
            }
        }
        return true;
    }
    
    private async actionOnDBItemWithoutIncomingItemForGlobal(itemName: string, itemInDB: any): Promise<boolean> {
        const syncStatus = itemInDB[FieldConstants.FieldSyncStatus] as number;
        switch (syncStatus) {
            case SyncStatus.none: {
                const result = await this.databaseManager.delete(DatabaseType.AppDb, itemName, "" + FieldConstants.FieldLid + "='" + itemInDB[FieldConstants.FieldLid] + "'");
                return result !== 0;
            }
            case SyncStatus.queued:
            case SyncStatus.sent:
            case SyncStatus.error: {
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const result = await this.databaseManager.delete(DatabaseType.AppDb, itemName, "" + FieldConstants.FieldLid + "='" + itemInDB[FieldConstants.FieldLid] + "'");
                    return result !== 0;
                }
                Logger.logError(
                    "PullPushQueryReconciler",
                    "actionOnDBItemWithoutIncomingItemForGlobal",
                    `Invalid case. Items in Object Status GLOBAL can only be in Sync Status NONE. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`
                );
                return false;
            }
            default:
                break;
        }
        return true;
    }

    private async actionOnDBItemWithoutIncomingItemForAdd(itemName: string, itemInDB: any): Promise<boolean> {
        const syncStatus = itemInDB[FieldConstants.FieldSyncStatus] as number;
        switch (syncStatus) {
            case SyncStatus.queued:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logError(
                        "PullPushQueryReconciler",
                        "actionOnDBItemWithoutIncomingItemForAdd",
                        `Server did not return this item, which was added in the device. Reconciler retaining this item. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`
                    );
                    return false;
                }
            case SyncStatus.none:
            case SyncStatus.sent:
            case SyncStatus.error: {
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    const result = await this.databaseManager.delete(DatabaseType.AppDb, itemName, "" + FieldConstants.FieldLid + "='" + itemInDB[FieldConstants.FieldLid] + "'");
                    return result !== 0;
                }
                Logger.logError(
                    "PullPushQueryReconciler",
                    "actionOnDBItemWithoutIncomingItemForAdd",
                    `Server did not return this item, which was added in the device. Reconciler retaining this item. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`
                );
                return false;
            }
            default:
                break;
        }
        return true;
    }

    private async actionOnDBItemWithoutIncomingItemForModifyOrDelete(itemName: string, itemInDB: any): Promise<boolean> {
        const syncStatus = itemInDB[FieldConstants.FieldSyncStatus] as number;
        switch (syncStatus) {
            case SyncStatus.none:
            case SyncStatus.queued:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    Logger.logError(
                        "RequestResponseReconciler",
                        "actionOnDBItemWithoutIncomingItemForModifyOrDelete",
                        `Invalid case. Cannot queue the same object again if the BE is waiting for a response. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${itemName}, Lid: ${itemInDB[FieldConstants.FieldLid]}`);
                    return false;
                }
            case SyncStatus.sent:
            case SyncStatus.error: {
                const result = await this.databaseManager.delete(DatabaseType.AppDb, itemName, "" + FieldConstants.FieldLid + "='" + itemInDB[FieldConstants.FieldLid] + "'");
                return result !== 0;
            }
            default:
                break;
        }
        return true;
    }

    private async handleDeletionOfAttachmentItemsInDBWithoutIncomingItems(
        structureMetas: StructureMeta[]
    ): Promise<void> {
        // Handle Items in DB that do not have a matching incoming item from server

        // Get all attachment table names
        const childrenTableNames = structureMetas
            .filter((element) => element.structureName.endsWith(ServiceConstants.AttachmentBE))
            .map((e) => e.structureName);

        Logger.logDebug(
            "PullPushQueryReconciler",
            "handleDeletionOfAttachmentItemsInDBWithoutIncomingItems",
            `Children Table Names Which Support Attachments. ${childrenTableNames.toString()}`
        );

        if (childrenTableNames.length === 0) {
            return;
        }

        for (const childTableName of childrenTableNames) {
            // Get all items from the child item table that does not have incoming items
            const incomingItemLids = this.incomingAttachmentItemLids[childTableName];
            let whereClauseForExcludingIncomingItems = `${FieldConstants.FieldFid} = '${this.entityInDB[FieldConstants.FieldLid]}'`;
            if (incomingItemLids && incomingItemLids.length > 0) {
                whereClauseForExcludingIncomingItems += ` AND ${FieldConstants.FieldLid} NOT IN (`;
                for (let i = 0; i < incomingItemLids.length; i++) {
                    if (i !== 0) {
                        whereClauseForExcludingIncomingItems += ",";
                    }
                    whereClauseForExcludingIncomingItems += `'${incomingItemLids[i]}'`;
                }
                whereClauseForExcludingIncomingItems += ")";
            }
            const itemsInDBWithoutIncomingItems = await this.databaseManager.select(DatabaseType.AppDb, childTableName, whereClauseForExcludingIncomingItems);
            if (itemsInDBWithoutIncomingItems.length > 0) {
                await this.actionOnAttachmentItemsInDBWithoutIncomingItems(childTableName, itemsInDBWithoutIncomingItems);
            }
        }
    }

    async actionOnAttachmentItemsInDBWithoutIncomingItems(attachmentItemName: string, attachmentItemsInDBWithoutIncomingItems: any[]): Promise<void> {
        for (const attachmentItemInDB of attachmentItemsInDBWithoutIncomingItems) {
            try {
                await this.databaseManager.delete(DatabaseType.FrameworkDb, "InfoMessage", `belid='${attachmentItemInDB[FieldConstants.FieldLid]}'`);
            } catch (e) {
                Logger.logError(
                    "PullPushQueryReconciler",
                    "actionOnAttachmentItemsInDBWithoutIncomingItems",
                    "Error while deleting info message. Error: " + JSON.stringify(e)
                );
            }
            const objectStatus = attachmentItemInDB[FieldConstants.FieldObjectStatus] as number;
            switch (objectStatus) {
                case ObjectStatus.global:
                    await this.actionOnDBAttachmentItemWithoutIncomingItemForGlobal(attachmentItemName, attachmentItemInDB);
                    break;

                case ObjectStatus.add:
                    await this.actionOnDBAttachmentItemWithoutIncomingItemForAdd(attachmentItemName, attachmentItemInDB);
                    break;

                case ObjectStatus.modify:
                    await this.actionOnDBAttachmentItemWithoutIncomingItemForModify(attachmentItemName, attachmentItemInDB);
                    break;

                case ObjectStatus.delete:
                    await this.actionOnDBAttachmentItemWithoutIncomingItemForDelete(attachmentItemName, attachmentItemInDB);
                    break;

                default:
                    break;
            }
        }
    }

    async actionOnDBAttachmentItemWithoutIncomingItemForGlobal(attachmentItemName: string, attachmentItemInDB: any): Promise<boolean> {
        const syncStatus = attachmentItemInDB[FieldConstants.FieldSyncStatus] as number;
        switch (syncStatus) {
            case SyncStatus.none: // Backend does not have this item
                {
                    return this.deleteAttachmentDataFromDB(attachmentItemName, attachmentItemInDB);
                }
            case SyncStatus.queued:
            case SyncStatus.sent:
            case SyncStatus.error:
                {
                    // Do not touch. Has to be handled by the Attachment upload queue separately. For ERROR application has to resolve.
                    Logger.logError(
                        "PullPushQueryReconciler",
                        "actionOnDBAttachmentItemWithoutIncomingItemForGlobal",
                        `Invalid case. Items in Object Status GLOBAL can only be in Sync Status NONE. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${attachmentItemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`
                    );
                }
                break;

            default:
                break;
        }
        return true;
    }

    async actionOnDBAttachmentItemWithoutIncomingItemForAdd(attachmentItemName: string, attachmentItemInDB: any): Promise<boolean> {
        const syncStatus = attachmentItemInDB[FieldConstants.FieldSyncStatus] as number;

        switch (syncStatus) {
            case SyncStatus.none:
                // This comment is valid for Request Response Reconciler
                // Backend does not have this time. Deleting the attachment.
                // This can happen during a sync submission.
            case SyncStatus.sent:
                if (this.reconcilerType == ReconcilerType.RequestResponse) {
                    return this.deleteAttachmentDataFromDB(attachmentItemName, attachmentItemInDB);
                }
            case SyncStatus.queued:
            case SyncStatus.error:
                {
                    var errormessage = `Server did not return this item, which was added in the device. Reconciler retaining this item. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${attachmentItemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`;
                    if (this.reconcilerType == ReconcilerType.RequestResponse) {
                        errormessage = `Invalid case. Items in Object Status MODIFY can only be in Sync Status SENT. Current Sync Status: ${syncStatus}, Header: ${this.entityName}, LID: ${this.entityInDB[FieldConstants.FieldLid]}, Item: ${attachmentItemName}, Lid: ${attachmentItemInDB[FieldConstants.FieldLid]}`;
                        return false;
                    }
                    Logger.logInfo("PullPushQueryReconciler", "actionOnDBAttachmentItemWithoutIncomingItemForAdd", errormessage);
                    return false;
                }
            default:
                break;
        }
        return true;
    }

    async actionOnDBAttachmentItemWithoutIncomingItemForModify(attachmentItemName: string, attachmentItemInDB: any): Promise<boolean> {
        const syncStatus = attachmentItemInDB[FieldConstants.FieldSyncStatus] as number;

        switch (syncStatus) {
            case SyncStatus.none:
            case SyncStatus.queued:
            case SyncStatus.sent:
            case SyncStatus.error:
                {
                    return this.deleteAttachmentDataFromDB(attachmentItemName, attachmentItemInDB);
                }
            default:
                break;
        }
        return true;
    }

    async actionOnDBAttachmentItemWithoutIncomingItemForDelete(attachmentItemName: string, attachmentItemInDB: any): Promise<boolean> {
        const syncStatus = attachmentItemInDB[FieldConstants.FieldSyncStatus] as number;

        switch (syncStatus) {
            case SyncStatus.none:
            case SyncStatus.queued:
            case SyncStatus.sent:
            case SyncStatus.error:
                {
                    return this.deleteAttachmentDataFromDB(attachmentItemName, attachmentItemInDB);
                }
            default:
                break;
        }
        return true;
    }

    private async deleteAttachmentDataFromDB(attachmentItemName: string, attachmentItemInDB: any): Promise<boolean> {
        try {
            const uid = attachmentItemInDB[ServiceConstants.AttachmentItemFieldUid] || "";
            var result = await this.databaseManager.delete(DatabaseType.AppDb, attachmentItemName, `${FieldConstants.FieldLid}='${attachmentItemInDB[FieldConstants.FieldLid]}'`);
            if (uid.length > 0) {
                result = await this.databaseManager.delete(DatabaseType.FrameworkDb, "AttachmentQObject", `uid ='${uid}'`);
            }
            return result !== 0;
        } catch (e) {
            Logger.logDebug("PullPushQueryReconciler", "deleteAttachmentDataFromDB", "Error while deleting attachment. Input data: " + attachmentItemInDB.toString() + ". Error: " + JSON.stringify(e));
            Logger.logError("PullPushQueryReconciler", "actionOnDBAttachmentItemWithoutIncomingItemForGlobal", "Error while deleting attachment.");
            return false
        }
    }

    async manageConflict(
        headerMeta: StructureMeta,
        currentDBHeader: { [key: string]: any },
        currentIncomingHeader: { [key: string]: any },
        currentIncomingItems: { [key: string]: any }
      ): Promise<boolean> {
        // 1
        // Store the Local Data in the CONFLICT BE Data Field.
        // Generate Message For Sending to Server.
        const beData: { [key: string]: any } = {};
        beData[headerMeta.structureName] = currentDBHeader;
        const childItems = await SyncInputDataManager.getChildData(
          currentDBHeader[FieldConstants.FieldLid],
          headerMeta.structureName,
          DataType.all
        );
        Object.assign(beData, childItems);
      
        const finalBeObject = {
          [headerMeta.beName]: [beData]
        };
        const jsonString = JSON.stringify(finalBeObject);
      
        const inputJSON = {
          beName: headerMeta.beName,
          beHeaderLid: currentDBHeader[FieldConstants.FieldLid],
          data: jsonString
        };
        const result = await this.databaseManager.insert(DatabaseType.FrameworkDb, "ConflictBE", inputJSON, true);
        if (result === 0) {
          return false;
        }
        this.conflictBe = inputJSON;
      
        // 2
        // Set the currentIncomingHeader's LID to currentDBHeader's LID
        currentIncomingHeader[FieldConstants.FieldLid] = currentDBHeader[FieldConstants.FieldLid];
      
        // 3
        // Replace Headers and Items with the incoming header and items
        const status = await this.updateDataIntoDB(headerMeta.structureName, currentIncomingHeader, `${FieldConstants.FieldLid}='${currentDBHeader[FieldConstants.FieldLid]}'`);
        if (!status) {
          return false;
        }
        const keys = Object.keys(currentIncomingItems);
        for (const key of keys) {
          if (!key.endsWith(ServiceConstants.AttachmentBE)) {
            // delete all the existing items except attachment items
            const deleteStatus = await this.databaseManager.delete(DatabaseType.AppDb, key);
            if (deleteStatus === 0) {
              return false;
            }
          }
          const itemsArray = currentIncomingItems[key];
          for (const value of itemsArray) {
            // Insert All Incoming Items
            const insertStatus = await this.databaseManager.insert(DatabaseType.AppDb, key, value, false);
            if (!insertStatus) {
              return false;
            }
          }
        }
        return true;
      }
}

export default Reconciler;
