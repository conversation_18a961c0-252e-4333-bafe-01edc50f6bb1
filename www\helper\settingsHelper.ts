import AttachmentQHelper from "../attachment/attachmentQHelper";
import { AuthenticationService } from "../authenticationService";
import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import InboxHelper from "../inbox/inboxHelper";
import OutBoxHelper from "../outbox/outboxHelper";
import OutboxService from "../outbox/outboxService";
import { SyncEngine } from "../syncEngine";
import FrameworkHelper from "./frameworkHelper";
import { FrameworkSettingsFields, FrameworkSettingsManager } from "./frameworkSettingsManager";
import { HttpConnection } from "./httpConnection";
import * as ServiceConstants from "./serviceConstants";
import { UserSettingsFields, UserSettingsManager } from "./userSettingsManager";
import { ObjectStatus, SyncStatus } from "./utils";

export enum LogLevel {
  debug = "DEBUG",
  error = "ERROR",
  important = "IMPORTANT"
}

declare var Logger: any;

class SettingsHelper {
  static async getFrameworkVersionNumber(): Promise<string> {
    return ServiceConstants.FrameworkVersionNumber;
  }

  static async getFrameworkBuildNumber(): Promise<string> {
    return ServiceConstants.FrameworkBuildNumber;
  }

  static async getApplicationName(): Promise<string> {
    return AuthenticationService.instance.loginParameters.appName;
  }

  static async getApplicationVersionNumber(): Promise<string> {
    return AuthenticationService.instance.loginParameters.appVersion;
  }

  static async getFrameworkRevisionNumber(): Promise<string> {
    return ServiceConstants.FrameworkRevisionNumber;
  }

  static async getApplicationRevisionNumber(): Promise<string> {
    return AuthenticationService.instance.loginParameters.appVersion;
  }

  static async getApplicationDBVersion(): Promise<string> {
    const databaseManager = DatabaseManager.getInstance();
    const allAppMetas = await databaseManager.select(DatabaseType.FrameworkDb, "ApplicationMeta");
    if (allAppMetas.length === 0) {
      return "";
    }
    return allAppMetas[0].version;
  }

  static async getApplicationBuildNumber(): Promise<string> {
    return ""
  }

  static async getInboxCount(): Promise<number> {
    return await DatabaseManager.getInstance().count(DatabaseType.FrameworkDb, "InObject");
  }

  static async getOutboxCount(): Promise<number> {
    return await DatabaseManager.getInstance().count(DatabaseType.FrameworkDb, "OutObject");
  }

  static async getSentItemsCount(): Promise<number> {
    return await DatabaseManager.getInstance().count(DatabaseType.FrameworkDb, "SentItems");
  }

  static async getAttachmentCount(): Promise<number> {
    return await DatabaseManager.getInstance().count(DatabaseType.FrameworkDb, "AttachmentQObject");
  }

  static async isInOutBoxQueue(beHeaderLid: string): Promise<boolean> {
    const outObject = await OutBoxHelper.checkIsInOutBox(beHeaderLid);
    return outObject !== null;
  }

  static async isInSentItems(beHeaderLid: string): Promise<boolean> {
    const sentItem = await OutBoxHelper.checkIsInSentItems(beHeaderLid);
    return sentItem !== null;
  }

  static async sendLogsToServer(): Promise<void> {
    // TODO: Implement this method
  }

  static async createAndGetLogZipPath(): Promise<String> {
    // TODO: Implement this method
    return "";
  }

  static async sendAppDbToServer(): Promise<void> {
    // TODO: Implement this method
  }

  static async deleteLogs(): Promise<void> {
    // TODO: Implement this method
  }
  
  static async requestInitialDataDownload(functions: Array<{ [key: string]: any }> = []): Promise<void> {
    const serverId = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.serverId);
    const applicationId = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.applicationId);
    const namespace = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.namespace);

    const dataObject = {
      "companyAlias": AuthenticationService.instance.loginParameters.company,
      "serverId": serverId,
      "applicationId": applicationId,
      "namespace": namespace,
      "applicationName": AuthenticationService.instance.loginParameters.appName,
      "type": 9000,
      "subtype": 400,
      "functions": functions
    };

    const inputJson: any = {};
    inputJson[ServiceConstants.QueryParamInputMessage] = JSON.stringify(dataObject)

    const dataString = Object.keys(dataObject).length === 0 ? "" : JSON.stringify(inputJson);

    const outObjectData = {
      lid: FrameworkHelper.getUUID(),
      timestamp: Date.now(),
      objectStatus: ObjectStatus.global,
      syncStatus: SyncStatus.none,
      functionName: ServiceConstants.AdminServiceInitialDownload,
      beName: "",
      beHeaderLid: "",
      requestType: "",
      syncType: ServiceConstants.SyncType.SYNC.toString(),
      conversationId: "",
      messageJson: dataString,
      companyNameSpace: "",
      sendStatus: "",
      fieldOutObjectStatus: ServiceConstants.OutObjectStatus.none.toString(),
      isAdminServices: true
    };

    try {
      const databaseManager = DatabaseManager.getInstance();
      await databaseManager.insert(DatabaseType.FrameworkDb, "OutObject", outObjectData, true);
      await Logger.logInfo("SettingsHelper", "requestInitialDataDownload", "Starting Outbox Service");
      OutboxService.getInstance().start();
    } catch (e) {
      throw e;
    }
  }

  static async testPushNotification(): Promise<void> {
    // TODO: Implement this method
  }

  static async getCompleteLogs(): Promise<string> {
    // TODO: Implement this method
    return "";
  }

  static async getInfoMessages(): Promise<any[]> {
    try {
        const databaseManager = DatabaseManager.getInstance();
        const infoMessages = await databaseManager.select(DatabaseType.FrameworkDb, "InfoMessage");
        Logger.logDebug("SettingsHelper", "getApplicationVersion", "Info Messages: " + infoMessages);
        return infoMessages;
    } catch (e) {
        Logger.logError("SettingsHelper", "getApplicationVersion", e);
        return [];
    }
  }

  static async setRequestTimeout(timeout: number): Promise<void> {
    try {
        Logger.logDebug("SettingsHelper", "setRequestTimeout", "Setting request timeout to " + timeout);
        await UserSettingsManager.getInstance().setFieldValue(UserSettingsFields.requestTimeout, timeout);
    } catch (e) {
        Logger.logError("SettingsHelper", "setRequestTimeout", e);
    }
  }

  static async getRequestTimeout(): Promise<number> {
    try {
        const requestTimeout = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.requestTimeout);
        return parseInt(requestTimeout);
    } catch (e) {
        Logger.logError("SettingsHelper", "getRequestTimeout", e);
        return 0;
    }
  }

  static setLogLevel(logLevel: string): void {
    Logger.logDebug(
        "SettingsHelper", "setLogLevel", `logLevel: ${logLevel}`);
    let logNumber = "";
    switch (logLevel) {
        case LogLevel.important:
        case "7":
            logNumber = "7";
            Logger.setLogLevel(LogLevel.important);
            break;
        case LogLevel.error:
        case "8":
            logNumber = "8";
            Logger.setLogLevel(LogLevel.error);
            break;
        case LogLevel.debug:
        case "9":
            logNumber = "9";
            Logger.setLogLevel(LogLevel.debug);
            break;
        default:
            // Handle default case if needed
            break;
    }
    (async () => {
      await FrameworkSettingsManager.getInstance()
        .setFieldValue(FrameworkSettingsFields.logLevel, logNumber);
    })
  }

  static async getLogLevel(): Promise<string> {
    try {
        const logLevel = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.logLevel);
        switch (logLevel) {
            case "7":
                return ServiceConstants.logImportant;
            case "8":
                return ServiceConstants.logError;
            case "9":
                return ServiceConstants.logDebug;
            default:
                return "";
        }    
    } catch (e) {
        Logger.logError("SettingsHelper", "getLogLevel", e);
        return "";
    }
  }

  static async getFetchInterval(): Promise<number> {
    try {
        const fetchInterval = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.fetchInterval);
        return fetchInterval ? parseInt(fetchInterval) : 0;
    } catch (e) {
        Logger.logError("SettingsHelper", "getFetchInterval", e);
        return 0;
    }
  }

  static async clearData() {
    await AuthenticationService.instance.clearData();
  }
  
  static async queuePingToOutbox(): Promise<void> {
    let outObjectData = {
      lid: FrameworkHelper.getUUID(),
      timestamp: Date.now(),
      objectStatus: ObjectStatus.global,
      syncStatus: SyncStatus.none,
      functionName: "",
      beName: "",
      beHeaderLid: "",
      requestType: "",
      syncType: ServiceConstants.SyncType.ASYNC.toString(),
      conversationId: "",
      messageJson: ServiceConstants.AdminServicePing,
      companyNameSpace: "",
      sendStatus: "",
      fieldOutObjectStatus: ServiceConstants.OutObjectStatus.none.toString(),
      isAdminServices: true
    }
    await new SyncEngine().checkInOutBoxAndQueue(outObjectData);
    return;
  }

  private async getAdditionalInfo(): Promise<string> {
    let infoMessageString = "";

    const databaseManager = DatabaseManager.getInstance();
    const infoMessages = await databaseManager.select(DatabaseType.FrameworkDb, "InfoMessage");
    infoMessageString += "Framework Version: BUILD 6\n";
    if (infoMessages.length > 0) {
      // Add the Info Message to the end of Data.

      for (let i = 0; i < infoMessages.length; i++) {
        if (i === 0) {
          infoMessageString += "********ALL INFO MESSAGES********";
        }

        const infoMessage = infoMessages[i];
        const category = infoMessage.category;
        const message = infoMessage.message;
        const beName = infoMessage.bename;
        const beLID = infoMessage.belid;
        infoMessageString += `\n${i + 1}`;
        infoMessageString += `\n${message}`;
        infoMessageString += `\n${category}`;
        infoMessageString += `\n${beName}`;
        infoMessageString += `\n${beLID}`;
        infoMessageString += "\n";
        if (i === infoMessages.length - 1) {
          infoMessageString += "********END OF INFO MESSAGES********\n";
        }
      }

      // Display All the Contents from ApplicationVersion.txt.
      infoMessageString += "********VERSION INFORMATION********\n";
      
      // TODO: Get the version and build number from the package info.
      const version = "";
      const buildNumber = "";

      infoMessageString += `Version: ${version}`;
      infoMessageString += `\tBuild: ${buildNumber}`;
    }

    infoMessageString += "\n";

    // Add inbox items, outbox items and sent items details
    const inboxItems = await databaseManager.select(DatabaseType.FrameworkDb, "InObject");
    if (inboxItems.length > 0) {
      let inboxStr = "***** INBOX *****\n";
      for (const inObj of inboxItems) {
        const currentDate = new Date(inObj.timestamp);
        const formatter = new Intl.DateTimeFormat('en-US', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const finalDate = formatter.format(currentDate);
        inboxStr += `${finalDate} | ${inObj.conversationId}\n`;
      }
      inboxStr += "\n";
      infoMessageString += inboxStr;
    }

    const outboxItems = await databaseManager.select(DatabaseType.FrameworkDb, "OutObject");
    if (outboxItems.length > 0) {
      let outboxStr = "***** OUTBOX *****\n";
      for (const outObj of outboxItems) {
        const currentDate = new Date(outObj.timestamp);
        const formatter = new Intl.DateTimeFormat('en-US', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const finalDate = formatter.format(currentDate);
        outboxStr += `${finalDate} | ${outObj.conversationId}\n`;
      }
      outboxStr += "\n";
      infoMessageString += outboxStr;
    }

    const sentItems = await databaseManager.select(DatabaseType.FrameworkDb, "SentItems");
    if (sentItems.length > 0) {
      let sentItemsStr = "***** SENT ITEMS *****\n";
      for (const sentItem of sentItems) {
        const currentDate = new Date(sentItem.timestamp);
        const formatter = new Intl.DateTimeFormat('en-US', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const finalDate = formatter.format(currentDate);
        sentItemsStr += `${finalDate} | ${sentItem.conversationId}\n`;
      }
      sentItemsStr += "\n";
      infoMessageString += sentItemsStr;
    }

    return infoMessageString;
  }

  convertMapToBase64(map: { [key: string]: any }): string {
    return btoa(JSON.stringify(map));
  }

  convertBase64ToMap(base64String: string): { [key: string]: any } {
    return JSON.parse(atob(base64String));
  }
}

export default SettingsHelper;