import FrameworkHelper from "../helper/frameworkHelper";
import * as ServiceConstants from "../helper/serviceConstants";
import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import { UnviredAccountManager } from "../helper/unviredAccountManager";
import { AuthenticationService } from "../authenticationService";
import { FrameworkSettingsFields, FrameworkSettingsManager } from "../helper/frameworkSettingsManager";
import { ObjectStatus, SyncStatus } from "../helper/utils";
import { HttpConnection } from "../helper/httpConnection";
import { Status } from "../helper/status";
import AttachmentHelper from "./attachmentHelper";
import { kAttachmentItem, kError, NotificationListenerHelper, NotificationListnerType } from "../helper/notificationListnerHelper";

declare var Logger: any;

const fileName = "AttachmentQHelper";
export default class AttachmentQHelper {
    static async checkAttachmentAndQueueForAutoDownload(
        attachmentItemName: string, attachmentItem: any): Promise<void> {
        if (FrameworkHelper.getPlatform() === "browser") {
            await Logger.logError(fileName, "checkAttachmentAndQueueForAutoDownload", "Attachment download is not supported in Web.");
            return;
        }

        attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = ServiceConstants.AttachmentStatusDefault;
        try {
            await DatabaseManager.getInstance().update(DatabaseType.AppDb, attachmentItemName, attachmentItem, `${ServiceConstants.AttachmentItemFieldUid} = '${attachmentItem[ServiceConstants.AttachmentItemFieldUid]}'`);
        } catch (e) {
            await Logger.logError(
                "AttachmentHelper",
                "checkAttachmentAndQueueForAutodownload",
                `Error while updating the status of the attachment item. Error: ${e}`);
        }

        // Check if it is marked for auto download and queue request if required
        const autoDownload = attachmentItem[ServiceConstants.AttachmentItemFieldAutoDownload];
        if (autoDownload != null && autoDownload === "true") {
            const isDatabaseOperationSuccessful = await AttachmentQHelper.queueForDownload(
                attachmentItemName, attachmentItem, ServiceConstants.FwAttachmentAutoDownloadPriority);
            if (!isDatabaseOperationSuccessful) {
                await Logger.logError(
                    "AttachmentHelper",
                    "checkAttachmentAndQueueForAutodownload",
                    "Failed to Queue Attachment Item for Download");
            }
        }
    }

    static async queueForDownload(attachmentItemName: string, attachmentItem: any, priority: number): Promise<boolean> {
        //  In Demo Mode, Attachments cannot be queued for Download.
        //  Therefore we log an Error Message saying that this feature is not supported in demo Mode.

        const demoMode = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.isDemo);
        if (demoMode) {
            // Create an Error Object.
            attachmentItem[ServiceConstants.AttachmentItemFieldMessage] =
                "Downloading of attachments is not supported in demo mode.";
            attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] =
                ServiceConstants.AttachmentStatusErrorInDownload;

            // Update the Attachment in the Database as well.
            try {
                await DatabaseManager.getInstance().update(DatabaseType.AppDb, attachmentItemName, attachmentItem, `${ServiceConstants.AttachmentItemFieldUid} = '${attachmentItem[ServiceConstants.AttachmentItemFieldUid]}'`);
            } catch (e) {
                await Logger.logError("AttachmentHelper", "queueForDownload",
                    `There was an error while downloading Attachments. Error: ${e}`);
            }
            const notifData = {
                [kAttachmentItem]: attachmentItem,
                [kError]: "Downloading of attachments is not supported in demo mode."
            }
            NotificationListenerHelper.postDataSenderNotification(notifData, NotificationListnerType.attachmentDownloadError);
            return true;
        }
        if (attachmentItem == null || Object.keys(attachmentItem).length === 0) {
            throw new Error("Attachment Item is null.");
        }
        await AttachmentQHelper.addAttachmentQForDownload(attachmentItemName, attachmentItem, priority);
        return true;
    }

    private static async addAttachmentQForDownload(attachmentItemName: string, attachmentItem: any, priority: number): Promise<void> {
        const structMetas = await DatabaseManager.getInstance().select(DatabaseType.FrameworkDb, "StructureMeta");
        //get AttachmentStructure
        const attachmentStructMeta = structMetas.find(
            (element) => element.structureName === attachmentItemName);
        if (!attachmentStructMeta) {
            await Logger.logError("AttachmentDownloader", "queueForDownload",
                `Structure Meta for Attachment Item (${attachmentItemName}) is not found.`);
            return;
        }
        //get BEHeader
        const headerStructMeta = structMetas.find(
            (element) =>
                element.beName === attachmentStructMeta.beName &&
                element.isHeader === "1");
        if (!headerStructMeta) {
            await Logger.logError("AttachmentDownloader", "queueForDownload",
                `Header Structure Meta for Attachment Item (${attachmentItemName}) is not found.`);
            return;
        }
        const timestamp = Date.now();
        const uid = attachmentItem[ServiceConstants.AttachmentItemFieldUid] ?? "";

        //create attachmentQObject and save it
        const attachmentQObjectData = {
            lid: FrameworkHelper.getUUID(),
            timestamp: timestamp,
            objectStatus: ObjectStatus.add,
            syncStatus: SyncStatus.none,
            uid: uid,
            beName: attachmentStructMeta.beName,
            beHeaderName: headerStructMeta.structureName,
            beAttachmentStructName: attachmentStructMeta.structureName,
            priority: priority,
            timeStamp: timestamp
        };
        await Logger.logDebug("AttachmentDownloader", "queueForDownload",
            `ATTACHMENT QUEUED LID ${attachmentQObjectData.lid} HEADER NAME : ${headerStructMeta.structureName} STRUCT NAME : ${attachmentStructMeta.structureName}`);

        try {
            // Add the Attachment Q Object to the Attachment Q
            await DatabaseManager.getInstance().insert(DatabaseType.FrameworkDb, "AttachmentQObject", attachmentQObjectData, true);
        } catch (e) {
            await Logger.logDebug("AttachmentDownloader", "queueForDownload",
                `ATTACHMENT QUEUE FAILED...... LID ${attachmentQObjectData.lid} HEADER NAME : ${headerStructMeta.structureName} STRUCT NAME : ${attachmentStructMeta.structureName}`);
            await Logger.logError("AttachmentDownloader", "queueForDownload",
                `Error while adding attachment Q object to the Attachment Q. Error: ${e}`);
            return;
        }
        //change status to queued for download
        attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] =
            ServiceConstants.AttachmentStatusQueuedForDownload;

        // Update the Attachment Status
        try {
            await DatabaseManager.getInstance().update(DatabaseType.AppDb, attachmentItemName, attachmentItem, `${ServiceConstants.AttachmentItemFieldUid} = '${attachmentItem[ServiceConstants.AttachmentItemFieldUid]}'`);
        } catch (e) {
            await Logger.logError("AttachmentDownloader", "queueForDownload",
                `DB Error while trying to mark Attachment as Queued for download. Error: ${e}`);
        }
        // call start AttachmentDownloadThread
        //start();
    }

    static async downloadAttachmentAndGetPath(uid: string, fileName: string): Promise<any> {
        const result = await new HttpConnection().downloadAttachment(uid);
        if (result.status === Status.ok) {
            const arrayBuffer = await result.arrayBuffer();
            const attachmentPath = await AttachmentHelper.addAttachment(fileName, arrayBuffer);
            return attachmentPath;
        } else {
            await Logger.logDebug("AttachmentHelper", "downloadAttachmentAndGetPath",
                `Failed to download attachment. Result: ${result.toString()}`);
            await Logger.logError("AttachmentHelper", "downloadAttachmentAndGetPath",
                'Failed to download attachment.');
            throw result;
        }
    }
}