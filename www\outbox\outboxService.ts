import DatabaseManager, { DatabaseType } from '../database/databaseManager';
import FrameworkHelper from '../helper/frameworkHelper';
import { FetchAPIResponse, HttpConnection } from '../helper/httpConnection';
import { RequestType } from '../helper/serviceConstants';
import { Status } from '../helper/status';
import { UnviredAccount } from '../helper/unviredAccount';
import { UnviredAccountManager } from '../helper/unviredAccountManager';
import { ObjectStatus, SyncStatus, isServerReachable } from '../helper/utils';
import * as ServiceConstants from '../helper/serviceConstants';
import * as FieldConstants from '../applicationMeta/fieldConstants';
import OutBoxHelper from './outboxHelper';
import OutboxAttachmentManager from './outboxAttachmentManager';
import { AuthenticationService } from '../authenticationService';
import InboxHelper from '../inbox/inboxHelper';
import DownloadMessageService from '../inbox/downloadMessageService';
import { SyncEngine } from '../syncEngine';
import { DATA, ERROR, ERROR_DETAIL, MESSAGE, NotificationListenerHelper, NotificationListnerType, TYPE } from '../helper/notificationListnerHelper';

declare var Logger: any;

const fileName = "OutboxService"

export default class OutboxService {

  private static instance: OutboxService;
  private isRunning: boolean = false;
  private shouldStop: boolean = false;

  private constructor() {}

  public static getInstance(): OutboxService {
    if (!OutboxService.instance) {
      OutboxService.instance = new OutboxService();
    }
    return OutboxService.instance;
  }

  public start(): void {
    if (this.isRunning) {
      Logger.logInfo(fileName, "start", "Download message service is already running.");
      return;
    }
    this.isRunning = true;
    this.shouldStop = false;
    this.makeNetworkCallWithOutObject();
    Logger.logInfo(fileName, "start", "Download message service started.");
  }

  public stop(): void {
    if (this.isRunning) {
      this.shouldStop = true;
      Logger.logInfo(fileName, "stop", "Stopping download message service.");
    } else {
        Logger.logInfo(fileName, "stop", "Download message service is not running.");
    }
  }

  private async makeNetworkCallWithOutObject() {
    let isOutboxEmpty = false;
    let data = {};
    do {
      if (this.shouldStop) {
        Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", "Stopping to process outbox items...");
        break;
      }
      Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", "Starting to process outbox items...");
      const databaseManager = DatabaseManager.getInstance();
      const outObjectCount = await databaseManager.count(DatabaseType.FrameworkDb, "OutObject");
      if (outObjectCount > 0) {
        NotificationListenerHelper.postSynchronizationStateNotification(`sending (${outObjectCount})`);
      }
      const outObject = await databaseManager.getFirstEntity(DatabaseType.FrameworkDb, "OutObject");
      isOutboxEmpty = outObject === null;
      if (outObject !== null) {
        const outObjectData = outObject;
        Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `OutObject to send: ${outObjectData.beName} - ${outObjectData.beHeaderLid} - outobject lid ${outObjectData.lid}`);
        try {
          if (!navigator.onLine) {
            break;
          }
          const lastLoggedInAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
          if (!(await isServerReachable(lastLoggedInAccount.getServerURL()))) {
            break;
          }
          if (outObjectData.isAdminServices) {
            const result = await HttpConnection.makeAdminServicesCall(outObjectData.messageJson, outObjectData.functionName, lastLoggedInAccount, AuthenticationService.instance.loginParameters);
            await this.deleteOutObject(outObjectData);
          } else {
            if (outObjectData.requestType === RequestType.RQST.toString() || outObjectData.requestType === RequestType.REQ.toString()) {
              const attachmentUploadSuccess = await new OutboxAttachmentManager().checkAndUploadAttachmentsInOutBox(outObjectData.beName, JSON.parse(outObjectData.messageJson));
              if (!attachmentUploadSuccess) {
                Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", "Failed to upload attachments");
                await this.createInfoMessage(outObjectData, "Failed to upload attachments", FieldConstants.InfoMessageFailure);
                await OutBoxHelper.updateSyncStatusToEntityObjects(outObjectData, SyncStatus.error);
                await this.deleteOutObject(outObjectData);
                continue;
              }
            }
            let retrySendOnConflict = false;
            do {
              if (this.shouldStop) {
                Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", "Stop requested. Exiting outbox processing loop.");
                break;
              }
              if (!navigator.onLine) {
                Logger.logError("OutboxService", "checkOutBoxAndSendToServer", "No internet connection to process outbox items...");
                break;
              }
              const lastLoggedInAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
              if (!(await isServerReachable(lastLoggedInAccount.getServerURL()))) {
                Logger.logError("OutboxService", "makeNetworkCallWithOutObject", "Server not reachable.");
                break;
              }
              const result = await new HttpConnection().makeSyncCall(outObjectData.messageJson, outObjectData.functionName, lastLoggedInAccount, AuthenticationService.instance.loginParameters);
              Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `DataSender submitRequest: ${result.httpStatusCode} HeaderLid : ${outObjectData.beHeaderLid}`);
              switch (result.httpStatusCode) {
                case Status.created:
                case Status.noContent:
                  await this.handleSuccess(result, outObjectData);
                  break;
                case 0:
                  await this.handleZeroResponse(result, outObjectData);
                  break;
                case Status.conflict:
                  await new Promise(resolve => setTimeout(resolve, 5000));
                  Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `Retrying after 5 seconds. Retry count: ${data[outObjectData.beHeaderLid]}`);
                  if (data[outObjectData.beHeaderLid] == null) {
                    data[outObjectData.beHeaderLid] = 0;
                  }
                  retrySendOnConflict = data[outObjectData.beHeaderLid] < 12;
                  data[outObjectData.beHeaderLid] += 1;
                  Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `Got Status Code: 409 from server. Waiting for 5 seconds and restart the process.`);
                  if (data[outObjectData.beHeaderLid] >= 12) {
                    await OutBoxHelper.updateSyncStatusToEntityObjects(outObjectData, SyncStatus.error);
                    await this.deleteOutObjectAndCreateInfoMessage(outObjectData, "Failed to synchronize with server within 1 minute. Please submit your request again.", true);
                  }
                  break;
                case Status.badRequest:
                case Status.unauthorized:
                case Status.forbidden:
                case Status.notFound:
                case Status.notAcceptable:
                case Status.unsupportedMediaType:
                case Status.gone:
                default:
                  await this.handleServerError(result, outObjectData);
                  break;
              }
            } while (retrySendOnConflict);
            if (data[outObjectData.beHeaderLid] != null) {
              delete data[outObjectData.beHeaderLid];
            }
          }
        } catch (e) {
          Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `Exception when processing outObject Message JSON: ${outObjectData.messageJson}`);
          Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", `Exception when processing outObject. MESSAGE : ${e.toString()}... Outobjec Data: ${outObjectData.lid} ${JSON.stringify(outObjectData)}`);
          await this.createInfoMessage(outObjectData, e.toString(), FieldConstants.InfoMessageFailure);
          await this.updateOutObjectProcessingError(outObjectData);
          isOutboxEmpty = true
        }
      }
    } while (!isOutboxEmpty);
    Logger.logInfo("OutboxService", "makeNetworkCallWithOutObject", "Outbox is empty. Stopping outbox processing service.")
    NotificationListenerHelper.postSynchronizationStateNotification(`idle`);
    this.isRunning = false;
    this.shouldStop = false;
    DownloadMessageService.getInstance().start();
  }

  private async handleServerError(result: FetchAPIResponse, outObjectData: any) {
    const responseObject = JSON.parse(result.data);
    if (responseObject[ServiceConstants.KeyInfoMessage] != null) {
      const infoMessages = responseObject[ServiceConstants.KeyInfoMessage];
      if (infoMessages.length > 0) {
        for (const element of infoMessages) {
          const category = element[FieldConstants.FieldInfoMessageCategory];
          const message = element[FieldConstants.FieldInfoMessageMessage] ?? "UNI_RESPONSE_ERROR_500_503";
          await this.createInfoMessage(outObjectData, message, category);
        }
      }
      // No need to Post Notification to the app
    } else if (responseObject[ServiceConstants.KeyError] != null) {
      // No need to Post Notification to the app
    }
    await this.deleteOutObject(outObjectData);
  }

  private async handleZeroResponse(result: FetchAPIResponse, outObjectData: any) {
    const errorMessage = this.getResponseText(result.httpStatusCode);
    Logger.logError("OutboxService", "handleZeroResponse", `HTTP response error. Response code: ${result.httpStatusCode} Response message: ${errorMessage}`);
    const msg = `Function: ${outObjectData.functionName}. Message: ${errorMessage}`;
    await this.createInfoMessage(outObjectData, msg, FieldConstants.InfoMessageFailure);
    await this.deleteOutObject(outObjectData);
  }

  private async handleSuccess(result: FetchAPIResponse, outObjectData: any) {

    // if (FrameworkHelper.getPlatform() === "browser") {
    //     Logger.logInfo("OutboxService", "handleSuccess", "Process browser platform success resposne.");
    //     // TODO: Need to handle the success response for browser platform.
    //     return;
    // }

    const headers = JSON.parse(result.header);
    var conversationId = headers[ServiceConstants.HeaderConstantConversationId.toLowerCase()];
    if (FrameworkHelper.getPlatform() == "browser") {
        conversationId = FrameworkHelper.getUUID();
    }
    if (outObjectData.syncType === ServiceConstants.SyncType.ASYNC.toString()) {
      if (!conversationId) {
        Logger.logError("OutboxService", "handleSuccess", "Conversation Id empty in http header fields.");
        await this.deleteOutObjectAndCreateInfoMessage(outObjectData, "Conversation id not available in response. Deleting out object.");
        return;
      }

      Logger.logInfo("OutboxService", "handleSuccess", `Conversation Id: ${conversationId}`);
      Logger.logInfo("OutboxService", "handleSuccess", `Deleting OutObject LID: ${outObjectData.lid}`);

      const deleteResult = await this.deleteOutObject(outObjectData);
      const databaseManager = DatabaseManager.getInstance()
    
      if (outObjectData.requestType === RequestType.RQST.toString()) {
        const sentItems = await databaseManager.select(DatabaseType.FrameworkDb, "SentItems", `conversationId='${conversationId}'`)
        if (sentItems.length > 0) {
          Logger.logInfo("OutboxService", "handleSuccess", `Conversation Id already present in sent items...ignoring Conversation Id: ${outObjectData.conversationId}`);
          return;
        }
        const structureMetas = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `structureName='${outObjectData.beName}'`);
        if (structureMetas.length == 0) {
          Logger.logError("OutboxService", "handleSuccess", `Invalid BE. Cannot Upload Attachments. Table Name: ${outObjectData.beName}`);
          return;
        }
        const isAttachmentSupported = await new OutboxAttachmentManager().isAttachmentSupportedForBEName(structureMetas[0].beName);
        let attachmentFlag = "";
        if (isAttachmentSupported) {
          attachmentFlag = "X";
        }
        try {
          if (FrameworkHelper.getPlatform() !== "browser") {
            Logger.logInfo("OutboxService", "handleSuccess", `Adding into sent items. Conversation Id: ${conversationId}`);
            const sentItem = {
              lid: FrameworkHelper.getUUID(),
              timestamp: Date.now(),
              objectStatus: 0,
              syncStatus: 0,
              beName: outObjectData.beName,
              beHeaderLid: outObjectData.beHeaderLid,
              conversationId,
              entryDate: Date.now().toString(),
              attachmentFlag
            };

            const result = await databaseManager.insert(DatabaseType.FrameworkDb, "SentItems", sentItem, true);

            if (result === 0) {
              Logger.logInfo("OutboxService", "handleSuccess", `Error while adding into sent items. Conversation Id: ${conversationId}`);
            }
          }

          await OutBoxHelper.updateSyncStatusToEntityObjects(outObjectData, SyncStatus.sent);

          let headerDataArray = []
          try {
            headerDataArray = await databaseManager.select(DatabaseType.AppDb, outObjectData.beName, `${FieldConstants.FieldLid}='${outObjectData.beHeaderLid}'`);
          } catch (e) {
            Logger.logError("OutboxService", "handleSuccess", `DBException caught when querying ${outObjectData.beName}: ${e.toString()}`);
          }
          NotificationListenerHelper.postDataSenderNotification(headerDataArray, NotificationListnerType.data_send);
        } catch (e) {
          Logger.logError("OutboxService", "handleSuccess", `Exception caught while adding sent item object to database. Deleting out object. OutObject LID: ${outObjectData.lid}, ${e.toString()}`);
          return;
        }
      } else if (outObjectData.requestType === RequestType.REQ.toString()) {
        // No need to Post Notification to the app
        await this.updateOutObjectBEWithGlobalStatus(outObjectData);
      } else {
        // No need to Post Notification to the app
      }
      if (FrameworkHelper.getPlatform() == "browser") {
        const responseData = JSON.parse(result.data);
        const headerObj = JSON.parse(result.header);
        const conversationId = headerObj[ServiceConstants.HeaderConstantConversationId.toLowerCase()];
        let requestType = headerObj[ServiceConstants.HeaderConstantRequestType.toLowerCase()];
        let type = -1;
        let subType = -1;
        let appId = "";
        let serverId = "";
        let applicationName = "";

        if (ServiceConstants.Type in responseData && ServiceConstants.Subtype in responseData) {
            type = responseData[ServiceConstants.Type];
            subType = responseData[ServiceConstants.Subtype];
            appId = responseData[ServiceConstants.ApplicationId];
            serverId = responseData[ServiceConstants.ServerId];
            applicationName = responseData[ServiceConstants.AppName];
        }

        if (headerObj[ServiceConstants.HeaderConstantPullMode] === ServiceConstants.PullModeDelete) {
            requestType = "PULL_D";
        }
        const inObjectData: any = {
          lid: FrameworkHelper.getUUID(),
          timestamp: Date.now(),
          objectStatus: ObjectStatus.global,
          syncStatus: SyncStatus.none,
          conversationId: conversationId,
          requestType: requestType,
          jsonData: outObjectData.beName,
          beLid: outObjectData.beHeaderLid,
          subtype: subType,
          serverId: serverId,
          type: type,
          appName: applicationName,
          appId: appId
        };

        const inObjectsArray = await databaseManager.select(DatabaseType.FrameworkDb, "InObject", `conversationId = '${conversationId}'`)
        if (inObjectsArray.length == 0) {
          try {
            await InboxHelper.addInBoxData(conversationId, JSON.stringify(responseData));
          }
          catch (e) {
              Logger.logError(fileName, "downloadMessage", `Failed to add inbox data to file. Error: ${JSON.stringify(e)}`);
              return false;
          }
          await databaseManager.insert(DatabaseType.FrameworkDb, "InObject", inObjectData, true);
        }
      }
    } else {
      await this.deleteOutObject(outObjectData);
    }
  }

  private async updateOutObjectBEWithGlobalStatus(outObjectData: any) {
    const databaseManager = DatabaseManager.getInstance()
    const structureMetas = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `structureName = '${outObjectData.beName}' AND isHeader = '1'`);
    if (structureMetas.length > 0) {
        let result = [];
        try {
            result = await databaseManager.select(DatabaseType.AppDb, outObjectData.beName, `${FieldConstants.FieldLid} = '${outObjectData.beHeaderLid}'`);
            console.log("OutboxService | updateOutObjectBEWithGlobalStatus | result: ", result);
        } catch (e) {
            Logger.logError("OutboxService", "updateOutObjectBEWithGlobalStatus", `DBException caught when querying ${outObjectData.beName}: ${e.toString()}`);
            return
        }
        if (result.length > 0) {
            try {
                const beData = result[0];
                await databaseManager.update(DatabaseType.AppDb, outObjectData.beName, {
                    [FieldConstants.FieldSyncStatus]: SyncStatus.none,
                    [FieldConstants.FieldInfoMsgCat]: ""
                }, `${FieldConstants.FieldLid}='${beData[FieldConstants.FieldLid]}'`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null.
                const childStructureMetas = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `beName = '${structureMetas[0].beName}' AND isHeader != '1'`)

                for (const childStructureMetaData of childStructureMetas) {
                    await databaseManager.update(DatabaseType.AppDb, childStructureMetaData.structureName, {
                        [FieldConstants.FieldSyncStatus]: SyncStatus.none
                    }, `${FieldConstants.FieldFid}='${beData[FieldConstants.FieldLid]}' AND ${FieldConstants.FieldObjectStatus} != ${ObjectStatus.global}`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null.
                }   
            } catch (e) {
                Logger.logError("OutboxService", "updateOutObjectBEWithGlobalStatus", `DBException caught when updating ${outObjectData.beName}: ${e.toString()}`);
            }
        } 
    }
  }

  private async deleteOutObjectAndCreateInfoMessage(outObjectData: any, message: string, isInfoRequired = false) {
    if (outObjectData != null) {
      await this.deleteOutObject(outObjectData);
      if (!isInfoRequired) {
        return;
      }
      try {
        const list = new TextEncoder().encode(outObjectData.messageJson);
        const bytes = new Uint8Array(list);
        const infoMessageData = {
          lid: FrameworkHelper.getUUID(),
          timestamp: Date.now(),
          objectStatus: 0,
          syncStatus: 0,
          type: "type",
          subtype: "subtype",
          category: FieldConstants.InfoMessageError,
          message,
          bename: outObjectData.beName,
          belid: outObjectData.beHeaderLid,
          messagedetails: bytes
        };
        const databaseManager = DatabaseManager.getInstance();
        await databaseManager.insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessageData, true);
        const infoMessageClientData = {
          LID: infoMessageData.lid,
          TIMESTAMP: infoMessageData.timestamp,
          OBJECT_STATUS: infoMessageData.objectStatus,
          SYNC_STATUS: infoMessageData.syncStatus,
          TYPE: infoMessageData.type,
          SUBTYPE: infoMessageData.subtype,
          CATEGORY: infoMessageData.category,
          MESSAGE: infoMessageData.message,
          BE_NAME: infoMessageData.bename,
          BE_LID: infoMessageData.belid,
          MESSAGE_BLOB: bytes
        };
        NotificationListenerHelper.postDataSenderNotification([infoMessageClientData], NotificationListnerType.infoMessage);
      } catch (e) {
        Logger.logInfo("OutboxService", "deleteOutObjectAndCreateInfoMessage", `DBException caught while inserting Info Message: ${JSON.stringify(e)}`);
      }
    }
  }

  private async updateOutObjectProcessingError(outObjectData: any) {
    try {
      outObjectData["fieldOutObjectStatus"] = ServiceConstants.OutObjectStatus.errorOnProcessing.toString();
      const databaseManager = DatabaseManager.getInstance();
      await databaseManager.update(DatabaseType.FrameworkDb, "OutObject", outObjectData, `${FieldConstants.FieldLid}='${outObjectData[FieldConstants.FieldLid]}'`);
      await OutBoxHelper.updateSyncStatusToEntityObjects(outObjectData, SyncStatus.error);
    } catch (e) {
      Logger.logInfo("OutboxService", "updateOutObjectProcessingError", `DBException caught while updating OutObject: ${JSON.stringify(e)}`);
    }
  }

  private async createInfoMessage(outObjectData: any, errorMessage: string, category: string) {
    const list = new TextEncoder().encode(outObjectData.messageJson);
    const bytes = new Uint8Array(list);
    const infoMessageData = {
      lid: FrameworkHelper.getUUID(),
      timestamp: Date.now(),
      objectStatus: ObjectStatus.global,
      syncStatus: SyncStatus.none,
      type: "",
      subtype: "",
      category,
      message: errorMessage,
      bename: outObjectData.beName,
      belid: outObjectData.beHeaderLid,
      messagedetails: bytes
    };
    const databaseManager = DatabaseManager.getInstance();
    await databaseManager.insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessageData, true);
  }

  private async deleteOutObject(outObjectData: any) {
    try {
      const databaseManager = DatabaseManager.getInstance();
      await databaseManager.delete(DatabaseType.FrameworkDb, "OutObject", `${FieldConstants.FieldLid}='${outObjectData[FieldConstants.FieldLid.toLowerCase()]}'`);
      Logger.logInfo("OutboxService", "deleteOutObject", `After delete is called. LID: ${outObjectData[FieldConstants.FieldLid.toLowerCase()]}`);
      return true;
    } catch (e) {
      Logger.logError("OutboxService", "deleteOutObject", `DBException caught while deleting OutObject: ${JSON.stringify(e)}`);
      return false;
    }
  }

  private getResponseText(responseCode: number) {
    let text = "";

    switch (responseCode) {
      case 0:
      case -1:
        text = "Cannot connect to server. Please try again later.";
        break;
      case 200:
        text = "Successful.";
        break;
      case 204:
        text = "No content. The server successfully processed the request, but is not returning any content.";
        break;
      case 400:
        text = "Bad request. The request could not be understood by the server due to malformed syntax.";
        break;
      case 401:
        text = "Unauthorized request. The request requires user authentication.";
        break;
      case 403:
        text = "Forbidden. The server understood the request, but is refusing to fulfill it.";
        break;
      case 404:
        text = "Not found. The server has not found anything matching the Request-URI.";
        break;
      case 405:
        text = "Method not allowed. The method specified in the Request-Line is not allowed for the resource identified by the Request-URI.";
        break;
      case 406:
        text = "Not acceptable.";
        break;
      case 407:
        text = "Proxy authentication required.";
        break;
      case 408:
        text = "Request timeout. The client did not produce a request within the time that the server was prepared to wait.";
        break;
      case 410:
        text = "Gone. The requested resource is no longer available at the server and no forwarding address is known.";
        break;
      case 500:
        text = "Internal server error. The server encountered an unexpected condition which prevented it from fulfilling the request.";
        break;
      case 501:
        text = "Not implemented. The server does not support the functionality required to fulfill the request.";
        break;
      case 502:
        text = "Bad gateway. The server, while acting as a gateway or proxy, received an invalid response from the upstream server it accessed in attempting to fulfill the request.";
        break;
      case 503:
        text = "Service unavailable. The server is currently unable to handle the request due to a temporary overloading or maintenance of the server.";
        break;
      case 504:
        text = "Gateway timeout. The server, while acting as a gateway or proxy, did not receive a timely response from the upstream server specified by the URI.";
        break;
      case 505:
        text = "HTTP version not supported. The server does not support, or refuses to support, the HTTP protocol version that was used in the request message.";
        break;
      default:
        break;
    }

    return text;
  }
}