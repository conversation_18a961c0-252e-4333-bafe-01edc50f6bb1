import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import * as FieldConstants from "../applicationMeta/fieldConstants";
import { ObjectStatus, SyncStatus } from "../helper/utils";
import { RequestType } from "../helper/serviceConstants";

declare var Logger: any;

const fileName = "OutBoxHelper";
export default class OutBoxHelper {
    static async checkIsInOutBox(beLid: string): Promise<any> {
        if (beLid === "") {
            return null;
        }
        const databaseManager = await DatabaseManager.getInstance();
        const outObjectData = await databaseManager.select(DatabaseType.FrameworkDb, "OutObject", `beHeaderLid = '${beLid}'`);
        return outObjectData.length > 0 ? outObjectData[0] : null;
    }

    static async checkIsInSentItems(beLid: string): Promise<any> {
        if (beLid === "") {
            return null;
        }
        const databaseManager = await DatabaseManager.getInstance();
        const sentItem = await databaseManager.select(DatabaseType.FrameworkDb, "SentItems", `beHeaderLid = '${beLid}'`);
        return sentItem.length > 0 ? sentItem[0] : null;
    }

    static async updateSyncStatusToEntityObjects(outObject: any, syncStatus: SyncStatus): Promise<void> {
        const requestType = outObject.requestType;
        const entityName = outObject.beName;
        const beLid = outObject.beHeaderLid;

        if (requestType === RequestType.RQST.toString()) {
            const databaseManager = await DatabaseManager.getInstance();
            const structureMetaData = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `structureName = '${entityName}'`);
            if (structureMetaData.length === 0) {
                await Logger.logDebug(
                    "OutboxHelper",
                    "updateSyncStatusToEntityObjects",
                    "No Business Entity got from database, BE-NAME: " +
                        outObject.beName +
                        ", BE-LID: " +
                        beLid);
                return;
            }
            const structureFieldData = await databaseManager.select(DatabaseType.AppDb, structureMetaData[0].structureName, `${FieldConstants.FieldLid} = '${beLid}'`);
            if (structureFieldData.length === 0) {
                await Logger.logDebug(
                    "OutboxHelper",
                    "updateSyncStatusToEntityObjects",
                    `No Business Entity got from database, BE-NAME: ${outObject.beName}, BE-LID: ${beLid}`
                );
                return;
            }
            const data = structureFieldData[0];

            data[FieldConstants.FieldSyncStatus] = syncStatus;
            await databaseManager.update(DatabaseType.AppDb, entityName, data, `${FieldConstants.FieldLid} = '${beLid}'`);

            const childStructureMetas = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `beName = '${structureMetaData[0].beName}' AND isHeader != 1`);
            for (const childStructureMetaData of childStructureMetas) {
                await databaseManager.update(DatabaseType.AppDb, childStructureMetaData.structureName, {
                    [FieldConstants.FieldSyncStatus]: syncStatus
                }, `${FieldConstants.FieldFid}='${beLid}' AND ${FieldConstants.FieldObjectStatus} <> ${ObjectStatus.global}`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null.
            }
        }
    }
}