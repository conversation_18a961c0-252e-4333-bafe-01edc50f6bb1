import * as ServiceConstants from './serviceConstants'
export class URLService {
    static sanitizeLoginURL(loginURL: string): string {
        if (loginURL.endsWith("UMP") || loginURL.endsWith("UMP/") || loginURL.endsWith("?local")) {
          return loginURL;
        }
        if (!loginURL.endsWith("/")) {
          loginURL += "/";
        }
        loginURL += "UMP";
        return loginURL;
    }
    
    static getBaseUrl(baseUrl: string): string {
        if (baseUrl == null || baseUrl == undefined || baseUrl.length === 0) {
          return "";
        }
        baseUrl = this.sanitizeLoginURL(baseUrl)
        if (!baseUrl.endsWith("/")) {
          baseUrl += "/";
        }
        baseUrl += ServiceConstants.ServiceApiVersion;
        return baseUrl;
    }
    
    static getSessionUrl(baseUrl: string): string {
        var sessionUrl = this.getBaseUrl(baseUrl);
        sessionUrl += ServiceConstants.ServiceSession;
        return sessionUrl;
    }
    
    static getApplicationUrl(baseUrl: string): string {
        var appUrl = this.getBaseUrl(baseUrl);
        appUrl += ServiceConstants.ServiceApplications;
        return appUrl;
    }
    
    static getPingUrl(baseUrl: string): string {
        var pingUrl = this.getBaseUrl(baseUrl);
        pingUrl += ServiceConstants.AdminServicePing;
        return pingUrl;
    }
}
