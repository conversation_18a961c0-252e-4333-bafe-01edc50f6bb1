import { UnviredAccount } from "./unviredAccount";

declare var Logger: any;

export class UnviredAccountManager {
    private fileName = "UnviredAccountManager"
    private storageKey: string;
    private lastLoggedInKey: string;
    private lastLoggedOutKey: string;

    private static instance: UnviredAccountManager;

    private constructor() {
        this.storageKey = 'unviredAccountData';
        this.lastLoggedInKey = 'lastLoggedInKey';
        this.lastLoggedOutKey = 'lastLoggedOutKey';
    }

    public static getInstance(): UnviredAccountManager {
        if (!UnviredAccountManager.instance) {
            UnviredAccountManager.instance = new UnviredAccountManager();
        }
        return UnviredAccountManager.instance;
    }
    
    saveAccount(account: UnviredAccount, appName: string): void {
        const serializedData = localStorage.getItem(this.storageKey);
        let accounts: any[] = [];
        if (serializedData) {
            accounts = JSON.parse(serializedData);
            if (accounts == null) {
                accounts = [];
            }
        }
        // Check if account exists (by username and company)
        const existingIndex = accounts.findIndex((acc: any) => acc.username === account.getUsername() && acc.company === account.getCompany());
        if (existingIndex !== -1) {
            // Update existing account
            accounts[existingIndex] = account.toJSON();
        } else {
            // Add new account
            accounts.push(account.toJSON());
        }
        localStorage.setItem(this.storageKey, JSON.stringify(accounts));
        localStorage.setItem(appName + "_token", account.getJwtToken());
    }

    getAccount(userName: string, company: string): UnviredAccount {
        const serializedData = localStorage.getItem(this.storageKey);
        if (serializedData === null || serializedData.length === 0) {
            return null;
        }
        const accounts:any[] = JSON.parse(serializedData).map((acc: any) => UnviredAccount.fromJSON(acc));
        const account = accounts.find((acc: UnviredAccount) => acc.getUsername() === userName && acc.getCompany() === company);
        return account ? account : null;
    }

    getAllAccounts(): UnviredAccount[] {
        const serializedData = localStorage.getItem(this.storageKey);
        if (serializedData === null || serializedData.length === 0) {
            return [];
        }
        const accounts:any[] = JSON.parse(serializedData).map((acc: any) => UnviredAccount.fromJSON(acc));
        return accounts ? accounts : [];
    }

    setLastLoggedInAccount(accountId: string): void {
        localStorage.setItem(this.lastLoggedInKey, accountId);
    }

    getLastLoggedInAccount(): UnviredAccount {
        const accountId = localStorage.getItem(this.lastLoggedInKey);
        if (accountId === null || accountId.length === 0) {
            Logger.logError(this.fileName, "getlastLoggedInAccount", "No account found to get last logged in account");
            return null;
        }
        const serializedData = localStorage.getItem(this.storageKey);
        if (serializedData === null || serializedData.length === 0) {
            Logger.logError(this.fileName, "getlastLoggedInAccount", "No accounts found to get last logged in account");
            return null;
        }
        const accounts:any[] = JSON.parse(serializedData).map((acc: any) => UnviredAccount.fromJSON(acc));
        const accountIndex = accounts.findIndex((acc: UnviredAccount) => acc.getAccountId() === accountId);
        if (accountIndex === -1) {
            Logger.logError(this.fileName, "getlastLoggedInAccount", "Account not found in accounts list");
            return null;
        }
        return accounts[accountIndex];
    }

    setLastLoggedOutAccount(accountId: string): void {
        localStorage.setItem(this.lastLoggedOutKey, accountId);
    }

    getlastLoggedOutAccount(): UnviredAccount {
        const accountId = localStorage.getItem(this.lastLoggedOutKey);
        if (accountId === null || accountId.length === 0) {
            Logger.logError(this.fileName, "getlastLoggedOutAccount", "No account found to get last logged out account");
            return null;
        }
        const serializedData = localStorage.getItem(this.storageKey);
        if (serializedData === null || serializedData.length === 0) {
            Logger.logError(this.fileName, "getlastLoggedOutAccount", "No accounts found to get last logged out account");
            return null;
        }
        const accounts:UnviredAccount[] = JSON.parse(serializedData).map((acc: any) => UnviredAccount.fromJSON(acc));
        const accountIndex = accounts.findIndex((acc: UnviredAccount) => acc.getAccountId() === accountId);
        if (accountIndex === -1) {
            Logger.logError(this.fileName, "getlastLoggedOutAccount", "Account not found in accounts list");
            return null;
        }
        return accounts[accountIndex];
    }

    deleteAccount(accountId: string): void {
        const serializedData = localStorage.getItem(this.storageKey);
        if (serializedData === null || serializedData.length === 0) {
            Logger.logError(this.fileName, "deleteAccount", "No accounts found to delete account");
            return;
        }
        let accounts:any[] = JSON.parse(serializedData).map((acc: any) => UnviredAccount.fromJSON(acc));
        const accountIndex = accounts.findIndex((acc: UnviredAccount) => acc.getAccountId() === accountId);
        if (accountIndex === -1) {
            Logger.logError(this.fileName, "deleteAccount", "Account not found in accounts list");
            return;
        }
        accounts.splice(accountIndex, 1);
        localStorage.setItem(this.storageKey, JSON.stringify(accounts));
    }
}