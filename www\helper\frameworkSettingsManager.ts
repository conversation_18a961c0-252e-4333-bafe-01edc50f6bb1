import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import FrameworkHelper from "./frameworkHelper";
import { ObjectStatus, SyncStatus } from "./utils";

declare var Logger: any;

export enum FrameworkSettingsFields {
    appLockTimeout,
    email,
    firstName,
    lastName,
    dataFormat,
    serverType,
    unviredUser,
    namespace,
    serverVersion,
    activationId,
    frontendIdentifier,
    feUser,
    frontendType,
    secKey,
    secLevel,
    logLevel,
    url,
    overrideUMPUrl,
    localPassword,
    frontendId,
    locationTracking,
    locationTrackingInterval,
    locationUploadInterval,
    locationTrackingDays,
    locationTrackingStart,
    locationTrackingEnd,
    loginLanguage,
    compressPostData,
    phone,
    profilePic,
    oneTimeToken,
    applicationId,
    md5Pwd,
    // extra fields
    isDemo,
    serverId,
    companyAlias,
    config
}

export const stringToFrameworkSettingsFields = (fieldName: string): FrameworkSettingsFields => {
    switch(fieldName) {
        case "appLockTimeout":
            return FrameworkSettingsFields.appLockTimeout;
        case "email":
            return FrameworkSettingsFields.email;
        case "firstName":
            return FrameworkSettingsFields.firstName;
        case "lastName":
            return FrameworkSettingsFields.lastName;
        case "dataFormat":
            return FrameworkSettingsFields.dataFormat;
        case "serverType":
            return FrameworkSettingsFields.serverType;
        case "unviredUser":
            return FrameworkSettingsFields.unviredUser;
        case "namespace":
            return FrameworkSettingsFields.namespace;
        case "serverVersion":
            return FrameworkSettingsFields.serverVersion;
        case "activationId":
            return FrameworkSettingsFields.activationId;
        case "frontendIdentifier":
            return FrameworkSettingsFields.frontendIdentifier;
        case "feUser":
            return FrameworkSettingsFields.feUser;
        case "frontendType":
            return FrameworkSettingsFields.frontendType;
        case "secKey":
            return FrameworkSettingsFields.secKey;
        case "secLevel":
            return FrameworkSettingsFields.secLevel;
        case "logLevel":
            return FrameworkSettingsFields.logLevel;
        case "url":
            return FrameworkSettingsFields.url;
        case "overrideUMPUrl":
            return FrameworkSettingsFields.overrideUMPUrl;
        case "localPassword":
            return FrameworkSettingsFields.localPassword;
        case "frontendId":
            return FrameworkSettingsFields.frontendId;
        case "locationTracking":
            return FrameworkSettingsFields.locationTracking;
        case "locationTrackingInterval":
            return FrameworkSettingsFields.locationTrackingInterval;
        case "locationUploadInterval":
            return FrameworkSettingsFields.locationUploadInterval;
        case "locationTrackingDays":
            return FrameworkSettingsFields.locationTrackingDays;
        case "locationTrackingStart":
            return FrameworkSettingsFields.locationTrackingStart;
        case "locationTrackingEnd":
            return FrameworkSettingsFields.locationTrackingEnd;
        case "loginLanguage":
            return FrameworkSettingsFields.loginLanguage;
        case "compressPostData":
            return FrameworkSettingsFields.compressPostData;
        case "phone":
            return FrameworkSettingsFields.phone;
        case "profilePic":
            return FrameworkSettingsFields.profilePic;
        case "oneTimeToken":
            return FrameworkSettingsFields.oneTimeToken;
        case "applicationId":
            return FrameworkSettingsFields.applicationId;
        case "md5Pwd":
            return FrameworkSettingsFields.md5Pwd;
        case "isDemo":
            return FrameworkSettingsFields.isDemo;
        case "serverId":
            return FrameworkSettingsFields.serverId;
        case "companyAlias":
            return FrameworkSettingsFields.companyAlias;
        case "config":
            return FrameworkSettingsFields.config;
        default:
            return null;
    }
}

export class FrameworkSetting {
    lid: string;
    timestamp: number;
    objectStatus: number;
    syncStatus: number;
    fieldName: string;
    fieldValue: string;

    constructor(
        lid: string,
        timestamp: number,
        objectStatus: number,
        syncStatus: number,
        fieldName: string,
        fieldValue: string
    ) {
        this.lid = lid;
        this.timestamp = timestamp;
        this.objectStatus = objectStatus;
        this.syncStatus = syncStatus;
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }
}

export class FrameworkSettingsManager {
    private fileName = "FrameworkSettingsManager";
    private frameworkSettingsHashtable: any = {};
    private databaseManager = DatabaseManager.getInstance();

    private static frameworkSettingsManager: FrameworkSettingsManager =
        new FrameworkSettingsManager();
    
    private constructor() {}

    public static getInstance(): FrameworkSettingsManager {
        return FrameworkSettingsManager.frameworkSettingsManager;
    }

    public async setFieldValue(field: FrameworkSettingsFields, fieldValue: any): Promise<void> {
        if (field == null) {
            return;
        }
        const fieldName: string = FrameworkSettingsFields[field];
        let fwSetting: any[];
        try {
            fwSetting = await this.databaseManager.select(DatabaseType.FrameworkDb, "FrameworkSettings", `fieldName = '${fieldName}'`)
        } catch (e) {
            Logger.logError(this.fileName, "setFieldValue", `Error while getting framework setting. Error: ${JSON.stringify(e)}`);
        }

        if (fwSetting === null || fwSetting.length === 0) {
            const fwSettingObject = {
                lid: FrameworkHelper.getUUID(),
                timestamp: Date.now(),
                objectStatus: ObjectStatus.global,
                syncStatus: SyncStatus.none,
                fieldName: fieldName,
                fieldValue: `${fieldValue}`
            };
            this.frameworkSettingsHashtable[fieldName] = fwSettingObject;
            await this.databaseManager.insert(DatabaseType.FrameworkDb, "FrameworkSettings", fwSettingObject, true);
        } else {
            const fwSettingObject = {
                lid: fwSetting[0].lid,
                timestamp: Date.now(),
                objectStatus: fwSetting[0].objectStatus,
                syncStatus: fwSetting[0].objectStatus,
                fieldName: fieldName,
                fieldValue: `${fieldValue}`
            };
            this.frameworkSettingsHashtable[fieldName] = fwSettingObject;
            await this.databaseManager.update(DatabaseType.FrameworkDb, "FrameworkSettings", fwSettingObject, `lid = '${fwSettingObject.lid}'`);
        }
    }

    public async getFieldValue(field: FrameworkSettingsFields): Promise<string> {
        if (field == null) {
            return "";
        }
        const fieldName: string = FrameworkSettingsFields[field];
        if (this.frameworkSettingsHashtable && this.frameworkSettingsHashtable[fieldName]) {
            return this.frameworkSettingsHashtable[fieldName].fieldValue;
        }
        try {
            const fwSetting = await this.databaseManager.select(DatabaseType.FrameworkDb, "FrameworkSettings", `fieldName = '${fieldName}'`)
            if (fwSetting === null || fwSetting.length === 0) {
                Logger.logError(this.fileName, "getFieldValue", `No framework setting found for field: ${fieldName}`);
                return "";
            }
            this.frameworkSettingsHashtable[fieldName] = fwSetting[0];
            return fwSetting[0].fieldValue;
        } catch (e) {
            Logger.logError(this.fileName, "getFieldValue", `Error while getting framework setting. Error: ${JSON.stringify(e)}`);
            return "";
        }
    }

    public async addSystemCredential(systemCredential: any): Promise<void> {
        try {
            await this.databaseManager.insert(DatabaseType.FrameworkDb, "SystemCredentials", systemCredential, true);
        } catch (e) {
            Logger.logError(this.fileName, "addSystemCredential", `Error while adding system credential. Error: ${JSON.stringify(e)}`);
        }
    }

    public async getAllSystemCredentials(): Promise<any[]> {
        try {
            const systemCredentials = await this.databaseManager.select(DatabaseType.FrameworkDb, "SystemCredentials")
            if (systemCredentials === null || systemCredentials.length === 0) {
                Logger.logError(this.fileName, "getSystemCredentials", `No system credentials found`);
                return [];
            }
            return systemCredentials;
        } catch (e) {
            Logger.logError(this.fileName, "getSystemCredentials", `Error while getting system credentials. Error: ${JSON.stringify(e)}`);
            return [];
        }
    }


    public static getDemoModeFwSettings(loginParameters: any): any {
        const map = [
            { name: "Inbox", group: "Messages", enabled: true },
            { name: "OutBox", group: "Messages", enabled: true },
            { name: "Sent", group: "Messages", enabled: true },
            { name: "Attachments", group: "Messages", enabled: true },
            { name: "Info Messages", group: "Messages", enabled: true },
            { name: "Request data", group: "Messages", enabled: true },
            { name: "Get message", group: "Messages", enabled: true },
            { name: "Timeout", group: "Others", enabled: true },
            { name: "Notification", group: "Others", enabled: true },
            { name: "Logs", group: "Others", enabled: true },
            { name: "Clear data", group: "Others", enabled: true },
            { name: "About", group: "Info", enabled: true },
            { name: "Appication Version", group: "Info", enabled: true },
            { name: "SDK version", group: "Info", enabled: true }
        ];
        const config = JSON.stringify(map);
        const tempFrameworkSettings = {
            settings: {
                activationId: "demo",
                companyAlias: loginParameters.company,
                isDemo: "true",
                frontendId: "demo",
                frontendType: FrameworkHelper.getFrontendType(),
                frontendIdentifier: "demo",
                feUser: loginParameters.feUserId,
                localPassword: "false",
                logLevel: "8",
                namespace: "UNVIRED",
                serverId: "demo",
                url: loginParameters.url,
                config: config,
                oneTimeToken: "Unvired"
            }
        };
        return tempFrameworkSettings;
    }
}
