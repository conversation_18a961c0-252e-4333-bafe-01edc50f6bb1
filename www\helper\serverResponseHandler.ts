import { FieldMeta, StructureMeta } from "../applicationMeta/applicationMetadataParser";
import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import { RequestType } from "./serviceConstants";
import * as FieldConstants from '../applicationMeta/fieldConstants';
import * as ServiceConstants from '../helper/serviceConstants';
import { LogType, ObjectStatus, SyncStatus } from "./utils";
import FrameworkHelper from "./frameworkHelper";
import Reconciler, { ReconcilerType } from "./reconciler";
import { SyncEngine } from "../syncEngine";
import AttachmentQHelper from "../attachment/attachmentQHelper";
import { NotificationListenerHelper, NotificationListnerType } from "./notificationListnerHelper";
import { ResultType } from "../authenticationService";

declare var Logger: any;

const EventFieldBeName = "beName";
const EventFieldBeLid = "lid";
const EventFieldActionType = "actionType";
const EventFieldRequestType = "requestType";
const EventFieldHeaderDataStructure = "headerDataStructure";

class ServerResponseHandler {
    static sourceClass: string = "ServerResponseHandler";
  
    static async handleResponseData(responseData: any, autoSave: boolean, isForeground: boolean, lid: string, entityName: string, requestType: RequestType) {
      Logger.logInfo(this.sourceClass, "handleResponseData", "Starting of Server response Handler.");
      var infoMessageString = "";
      if (Object.keys(responseData).length > 0) {
        Logger.logInfo(this.sourceClass, "handleResponseData", "Starting of Server response Handler(Response is not empty)");
        try {
          const databaseManager = await DatabaseManager.getInstance();
          const processedHeaders: any[] = [];
          let metaData: any = {};
          let infoMessages: any[] = [];
          if (responseData[ServiceConstants.KeyMeta] != null) {
            metaData = responseData[ServiceConstants.KeyMeta];
            delete responseData[ServiceConstants.KeyMeta];
          }
          let isInfoMessageFailure = false;
          let isInfoMessageFailureAndProcess = false;
          let infoMsgCategory = "";
          const beMetas = await DatabaseManager.getInstance().select(DatabaseType.FrameworkDb, "BusinessEntityMeta");
          var structureMetas = await DatabaseManager.getInstance().select(DatabaseType.FrameworkDb, "StructureMeta");
          const fieldMetas = await DatabaseManager.getInstance().select(DatabaseType.FrameworkDb, "FieldMeta");
  
          if (entityName && entityName.length > 0) {
            const structureMeta = structureMetas.find(
                (element) => element.structureName === entityName
            );
            if (!structureMeta) {
                const msgString = "Entity definition is not available in metadata.json";
                Logger.logError(this.sourceClass, "handleResponseData", msgString);
                throw new Error(msgString);
            }
            const beMeta = beMetas.find(
                (element) => element.beName === structureMeta.beName
            );
            if (!beMeta) {
                const msgString = "Business Entity Meta is not available in metadata.json";
                Logger.logError(this.sourceClass, "handleResponseData", msgString);
                throw new Error(msgString);
            }
            if (beMeta.save === "false") {
                lid = "";
                entityName = "";
            }
          }
    
          const structureMetaTemp: any[] = [];
          for (const structureMeta of structureMetas) {
              const beMeta = beMetas.find(
                  (element) => element.beName === structureMeta.beName
              );
              if (!beMeta) {
                  const msgString = "Business Entity Meta is not available in metadata.json";
                  Logger.logError(this.sourceClass, "handleResponseData", msgString);
                  throw new Error(msgString);
              }
              if (beMeta.save === "true") {
                  structureMetaTemp.push(structureMeta);
              }
          }
          structureMetas = structureMetaTemp;
          // Save InfoMessages
          if (responseData[ServiceConstants.KeyInfoMessage] != null) {
            infoMessages = responseData[ServiceConstants.KeyInfoMessage];
            if (infoMessages.length > 0) {
              const updatedInfoMessages: any[] = [];
              for (const element of infoMessages) {
                if (element[FieldConstants.FieldInfoMessageCategory] != null) {
                  if (this.getRank(element[FieldConstants.FieldInfoMessageCategory]) <
                    this.getRank(infoMsgCategory)) {
                    infoMsgCategory = element[FieldConstants.FieldInfoMessageCategory];
                  }
                }
  
                if (element[FieldConstants.FieldInfoMessageCategory] != null &&
                  element[FieldConstants.FieldInfoMessageCategory] === FieldConstants.InfoMessageFailure &&
                  !isInfoMessageFailure) {
                  isInfoMessageFailure = true;
                }
                if (element[FieldConstants.FieldInfoMessageCategory] != null &&
                  element[FieldConstants.FieldInfoMessageCategory] === FieldConstants.InfoMessageFailureAndProcess &&
                  !isInfoMessageFailureAndProcess) {
                  isInfoMessageFailureAndProcess = true;
                }
                infoMessageString += ((infoMessageString.length > 0 ? "\n" : "") + element[FieldConstants.FieldInfoMessageMessage]);
                const infoMessage = {
                    lid: FrameworkHelper.getUUID(),
                    timestamp: Date.now(),
                    objectStatus: ObjectStatus.global,
                    syncStatus: SyncStatus.none,
                    type: "",
                    subtype: "",
                    category: element[FieldConstants.FieldInfoMessageCategory],
                    message: element[FieldConstants.FieldInfoMessageMessage],
                    bename: entityName,
                    belid: lid,
                    messagedetails: ""
                };
                await databaseManager.insert(DatabaseType.FrameworkDb, 'InfoMessage', infoMessage, true);
                const infoMessageClientData = {
                  LID: infoMessage.lid,
                  TIME_STAMP: infoMessage.timestamp,
                  OBJECT_STATUS: infoMessage.objectStatus,
                  SYNC_STATUS: infoMessage.syncStatus,
                  TYPE: infoMessage.type,
                  SUBTYPE: infoMessage.subtype,
                  CATEGORY: infoMessage.category,
                  MESSAGE: infoMessage.message,
                  BE_NAME: infoMessage.bename,
                  BE_LID: infoMessage.belid,
                  MESSAGE_BLOB: infoMessage.messagedetails
                };
                updatedInfoMessages.push(infoMessageClientData);
              }
              if (!isForeground) {
                NotificationListenerHelper.postDataSenderNotification(updatedInfoMessages, NotificationListnerType.infoMessage);
              }
              this.updateInfoMessageCategoryInHeader(isForeground, entityName, lid, infoMsgCategory);
            }
            delete responseData[ServiceConstants.KeyInfoMessage];
          } else {
            this.updateInfoMessageCategoryInHeader(isForeground, entityName, lid, "");
          }
  
          if (isInfoMessageFailure || isInfoMessageFailureAndProcess) {
            if (requestType === RequestType.RQST) {
              Logger.logError(this.sourceClass, "handleResponseData", `Info Message Failure. Marking header sync status to error. Response Data: ${JSON.stringify(responseData)}`);
              const errorString =
                `Info Message Failure. Marking header sync status to error. BE name: ${entityName}. LID: ${lid}`;
              await this.updateErrorStatusInHeaderAndCreateInfoMessage(isForeground,
                 errorString, entityName, lid, infoMsgCategory);
            }
            if (!isForeground) {
              // TODO: Check If Post Notification to the app is required.
            }
            if (!isInfoMessageFailureAndProcess) {
              return {type: ResultType.error, message: infoMessageString};
            }
          }
  
          if (autoSave && Object.keys(responseData).length > 0) {
            
            if (requestType === RequestType.RQST) {
              const structureMeta = structureMetas.find(
                (element) => element.structureName === entityName
              );
              if (!structureMeta) {
                const msgString =
                  "Entity definition is not available in metadata.json";
                Logger.logError(this.sourceClass, "handleResponseData", msgString);
                throw new Error(msgString);
              }
              const hasAtleastOneBe = await this.checkAtleastOneBeAvailable(
                entityName, responseData, structureMeta
              );
              if (!hasAtleastOneBe) {
                const msgString =
                  "For Request type RQST, Response should contain at least one Input Entity. But the response data does not contain Input Entity.";
                Logger.logError(this.sourceClass, "handleResponseData", msgString);
                throw new Error(msgString);
              }
            }
  
            // Handle Metadata Node
            let metaDataDeleteFlag = false;
            let pullMessageMetaDataBENames: any[] = [];
  
            if (requestType === RequestType.PULLD) {
              if (Object.keys(metaData).length > 0) {
                const deletionRequiredFlag = metaData[ServiceConstants.KeyMetadataDelete];
                if (deletionRequiredFlag != null &&
                  deletionRequiredFlag.toLowerCase() === "true") {
                  metaDataDeleteFlag = true;
                }
                if (Object.keys(metaData).length > 0) {
                  pullMessageMetaDataBENames = metaData[ServiceConstants.KeyBeName];
                }
              }
            }
            if (metaDataDeleteFlag && requestType === RequestType.PULLD) {
              try {
                try {
                  await DatabaseManager.getInstance().beginTransaction(DatabaseType.FrameworkDb);
                  await DatabaseManager.getInstance().beginTransaction(DatabaseType.AppDb);
                } catch (e) {
                  Logger.logError(this.sourceClass, "handleResponseData", `RequestType.PULLD: Error while starting transaction. Error : ${JSON.stringify(e)}.`);
                  throw new Error("Error while starting transaction.");
                }
                Logger.logInfo(this.sourceClass, "handleResponseData", "Inside transaction start.");
                for (const beName of pullMessageMetaDataBENames) {
                  Logger.logInfo(this.sourceClass, "handleResponseData", `Deleting BE: ${beName}.`);
                  const currentBeMeta = beMetas.find((element) => element.beName === beName);
                  if (!currentBeMeta) {
                    Logger.logError(this.sourceClass, "handleResponseData", `There was an error while trying to get Business Entity Meta for BE: ${beName}`);
                  }
                  const currentHeaderStructureMeta = structureMetas.find((element) =>
                    element.beName === beName && element.isHeader === "1"
                  );
                  if (!currentHeaderStructureMeta) {
                    Logger.logError(this.sourceClass, "handleResponseData", `There was an error while trying to get Header Structure Meta for BE: ${beName}`);
                  }

                  const databaseManager = DatabaseManager.getInstance();
                  if (currentBeMeta!.conflictRules === FieldConstants.ConflictModeServerWins) {
                    Logger.logInfo(this.sourceClass, "handleResponseData", `Deleting the contents of ${currentHeaderStructureMeta!.structureName} table regardless of the state of the header.`);
                    const childTables = structureMetas
                      .filter((itemStruct) =>
                        itemStruct.beName === currentHeaderStructureMeta.beName &&
                        itemStruct.structureName !== currentHeaderStructureMeta.structureName
                      )
                      .map((e) => e.structureName);
                    try {
                      Logger.logInfo(this.sourceClass, "handleResponseData", "Before Deleting child tables.");
                      for (const childName of childTables) {
                        Logger.logInfo(this.sourceClass, "handleResponseData", `Deleting child table ${childName}.`);
                        await databaseManager.delete(DatabaseType.AppDb, childName);
                        Logger.logInfo(this.sourceClass, "handleResponseData", `Child table ${childName} deleted.`);
                      }
                      await databaseManager.delete(DatabaseType.AppDb, currentHeaderStructureMeta.structureName);
                      Logger.logInfo(this.sourceClass, "handleResponseData", `Header table ${currentHeaderStructureMeta.structureName} deleted.`);
                    } catch (e) {
                      Logger.logError(this.sourceClass, "handleResponseData", `Could not cleanup database for ${currentHeaderStructureMeta.structureName}.`);
                    }
                  } else {
                    Logger.logInfo(this.sourceClass, "handleResponseData", `Deleting the contents of ${currentHeaderStructureMeta!.structureName} table whose header is in GLOBAL state.`);
                    const whereClause = `${FieldConstants.FieldObjectStatus} = ${ObjectStatus.global}`
                    await databaseManager.delete(DatabaseType.AppDb, currentHeaderStructureMeta.structureName, whereClause);
                  }
                }
                try {
                  await DatabaseManager.getInstance().endTransaction(DatabaseType.AppDb);
                  await DatabaseManager.getInstance().endTransaction(DatabaseType.FrameworkDb);
                } catch (e) {
                  Logger.logError(this.sourceClass, "handleResponseData", `Error while ending transaction. Error : ${JSON.stringify(e)}.`);
                }
              } catch (e) {
                try {
                  await DatabaseManager.getInstance().rollbackTransaction(DatabaseType.AppDb);
                  await DatabaseManager.getInstance().rollbackTransaction(DatabaseType.FrameworkDb);
                } catch (e) {
                  Logger.logError(this.sourceClass, "handleResponseData", `Error while rolling back transaction. Error : ${JSON.stringify(e)}.`);
                }
                Logger.logError(this.sourceClass, "handleResponseData", `Error while deleting BE data. Error : ${JSON.stringify(e)}.`);
              }
            }
            const conflictBEs: any[] = [];
            const databaseManager = DatabaseManager.getInstance();
            const responseDataKeys = Object.keys(responseData);
            let headerInDB: any | null = null;
            try {
              try {
                await DatabaseManager.getInstance().beginTransaction(DatabaseType.FrameworkDb);
                await DatabaseManager.getInstance().beginTransaction(DatabaseType.AppDb);
              } catch (e) {
                Logger.logError(this.sourceClass, "handleResponseData", `Error while starting transaction. Error : ${JSON.stringify(e)}.`);
                return;
              }
              for (const key of responseDataKeys) {
                const value = responseData[key];
                const currentBEMeta = beMetas.find((element) => element.beName === key);
                Logger.logInfo(this.sourceClass, "handleResponseData", `Processing BE META ${currentBEMeta}`);
                if (currentBEMeta) {
                  const currentHeaderStructureMeta = structureMetas.find((element) =>
                    element.beName === key && element.isHeader === "1"
                  );
                  if (currentHeaderStructureMeta) {
                    Logger.logInfo(this.sourceClass, "handleResponseData", `Processing HEADER ${currentHeaderStructureMeta.structureName}`);
                    const beValues = value;
                    let index = 0;
                    for (const element of beValues) {
                      const beObject = element;
                      const headerObject = beObject[currentHeaderStructureMeta.structureName];

                      const actionType = beObject[ServiceConstants.KeyActionAttribute];
                      delete beObject[currentHeaderStructureMeta.structureName];
                      delete beObject[ServiceConstants.KeyActionAttribute];

                      if (!headerObject) {
                        Logger.logError(this.sourceClass, "handleResponseData", `Error while getting Header for BE name: ${key}. Continuing with next BE.`);
                        continue;
                      }
                      let headerInDatabase: any | null = null;

                      if (requestType !== RequestType.PULLD) {
                        try {
                          let currentHeaderLid = lid;
                          if (requestType === RequestType.RQST &&
                            entityName !== currentHeaderStructureMeta.structureName) {
                            currentHeaderLid = "";
                          }
                          headerInDatabase = await this.checkDuplicateBe(currentHeaderStructureMeta.structureName, fieldMetas, headerObject, requestType!, currentHeaderLid);
                          headerInDB = headerInDatabase;
                        } catch (e) {
                          Logger.logError(this.sourceClass, "handleResponseData", `Error while checking for duplicate BE name: ${key}. Error : ${JSON.stringify(e)}.`);
                        }
                      }

                      if (requestType !== RequestType.PULL &&
                        requestType !== RequestType.PULLD) {
                        if (headerInDatabase) {
                          if (actionType === ServiceConstants.ActionTypeD) {
                            const whereClause = `${FieldConstants.FieldLid} = '${headerInDatabase[FieldConstants.FieldLid]}'`
                            await databaseManager.delete(DatabaseType.AppDb, currentHeaderStructureMeta.structureName, whereClause);
                            // TODO: Post Notification to the app
                            continue;
                          } else {
                            headerObject[FieldConstants.FieldLid] = headerInDatabase[FieldConstants.FieldLid];
                          }
                          try {
                            const whereClause = `belid = '${headerInDatabase[FieldConstants.FieldLid]}'`
                            await databaseManager.delete(DatabaseType.FrameworkDb, "InfoMessage", whereClause);
                          } catch (e) {
                            Logger.logError(this.sourceClass, "handleResponseData", `Error while deleting info message. Error : ${JSON.stringify(e)}.`);
                          }
                        }
                      }
                      if (actionType !== ServiceConstants.ActionTypeD) {
                        if (!headerInDatabase) {
                          if (!headerObject[FieldConstants.FieldLid]) {
                            headerObject[FieldConstants.FieldLid] = FrameworkHelper.getUUID();
                          }
                          processedHeaders.push({
                            [EventFieldBeName]: currentHeaderStructureMeta.structureName,
                            [EventFieldBeLid]: headerObject[FieldConstants.FieldLid],
                            [EventFieldActionType]: actionType,
                            [EventFieldRequestType]: requestType,
                            [EventFieldHeaderDataStructure]: headerObject
                          });
                          Logger.logInfo(this.sourceClass, "handleResponseData", `Inserting BE DATA : ${currentHeaderStructureMeta.structureName} LID : ${headerObject[FieldConstants.FieldLid]} ${index.toString()}`);
                          index++;
                          await this.insertIncomingBeData(isForeground, currentHeaderStructureMeta.structureName, headerObject, beObject);
                        } else {
                          Logger.logInfo(this.sourceClass, "handleResponseData", `Reconciling BE DATA : ${currentHeaderStructureMeta.structureName} LID : ${headerObject[FieldConstants.FieldLid]} ${index.toString()}`);
                          index++;
                          processedHeaders.push({
                            [EventFieldBeName]: currentHeaderStructureMeta.structureName,
                            [EventFieldBeLid]: headerObject[FieldConstants.FieldLid],
                            [EventFieldActionType]: actionType,
                            [EventFieldRequestType]: requestType,
                            [EventFieldHeaderDataStructure]: headerInDatabase
                          });
                          if (requestType === RequestType.RQST &&
                            entityName === currentHeaderStructureMeta.structureName) {
                            const requestResponseReconciler = new Reconciler(currentHeaderStructureMeta.structureName,
                                headerInDatabase,
                                headerObject,
                                beObject,
                                currentBEMeta.conflictRules,
                                isForeground,
                                ReconcilerType.RequestResponse
                            );
                            const status = await requestResponseReconciler.reconcile(
                              structureMetas,
                              fieldMetas,
                              requestType!,
                              headerInDatabase[FieldConstants.FieldLid]
                            );
                            if (requestResponseReconciler.getConflictBe().length > 0) {
                              conflictBEs.push(
                                requestResponseReconciler.getConflictBe()
                              );
                            }
                            if (!status) {
                              Logger.logError(this.sourceClass, "handleResponseData", `Reconciliation failed (RequestResponseReconciler). Rolling back db operation. Updating header sync status to error. BE Name: ${entityName}, headerInDatabase: ${JSON.stringify(headerInDatabase)}`);
                              const errorString =
                                `Reconciliation failed (RequestResponseReconciler). Rolling back db operation. Updating header sync status to error. BE Name: ${entityName}, headerInDatabase(lid): ${headerInDatabase[FieldConstants.FieldLid]}`;
                              throw new Error(errorString);
                            }
                          } else {
                            const pullPushQueryReconciler = new Reconciler(
                              currentHeaderStructureMeta.structureName,
                              headerInDatabase,
                              headerObject,
                              beObject,
                              currentBEMeta.conflictRules,
                              isForeground,
                              ReconcilerType.PullPushQuery
                            );
                            const status = await pullPushQueryReconciler.reconcile(
                              structureMetas,
                              fieldMetas,
                              requestType!,
                              headerInDatabase[FieldConstants.FieldLid]
                            );
                            if (!status) {
                              Logger.logError(this.sourceClass, "handleResponseData", `Reconciliation failed (PullPushQueryReconciler). Rolling back db operation. Updating header sync status to error. BE Name: ${entityName}, headerInDatabase: ${JSON.stringify(headerInDatabase)}`);
                              const errorString =
                                `Reconciliation failed (PullPushQueryReconciler). Rolling back db operation. Updating header sync status to error. BE Name: ${entityName}, headerInDatabase(lid): ${headerInDatabase[FieldConstants.FieldLid]}`;
                              throw new Error(errorString);
                            }
                          }
                        }
                      }
                    }
                  }
                } else {
                  Logger.logInfo(this.sourceClass, "handleResponseData", `BE which is not defined in metadata.json: ${JSON.stringify(responseData)}`);
                  // processedHeaders.push(responseData);
                }
              }
              try {
                await DatabaseManager.getInstance().endTransaction(DatabaseType.AppDb);
                await DatabaseManager.getInstance().endTransaction(DatabaseType.FrameworkDb);
              } catch (e) {
                Logger.logError(this.sourceClass, "handleResponseData", `Error while ending transaction. Error : ${JSON.stringify(e)}.`);
              }
            } catch (e) {
              try {
                await DatabaseManager.getInstance().rollbackTransaction(DatabaseType.AppDb);
                await DatabaseManager.getInstance().rollbackTransaction(DatabaseType.FrameworkDb);
              } catch (e) {
                Logger.logError(this.sourceClass, "handleResponseData", `Error while rolling back transaction. Error : ${JSON.stringify(e)}.`);
              }
              let headerLid = lid;
              if (headerInDB != null) {
                headerLid = headerInDB[FieldConstants.FieldLid] ?? "";
              }
              const infoMessageData = await this.updateErrorStatusInHeaderAndCreateInfoMessage(
                isForeground,
                JSON.stringify(e),
                entityName,
                headerLid,
                FieldConstants.InfoMessageFailure
              );
              await DatabaseManager.getInstance().insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessageData, true);
            }
            // TODO: set conflict handler
            // const conflictHandler = SyncEngine.getConflictHandler();
            // if (conflictHandler != null) {
            //   conflictHandler.handleConflict(conflictBEs);
            // }
          }
          if (processedHeaders.length > 0 && !isForeground) {
            NotificationListenerHelper.postDataSenderNotification(processedHeaders, NotificationListnerType.data_changed);
          }
        } catch (e) {
          Logger.logError(this.sourceClass, "handleResponseData", `Common catch block. Error: ${JSON.stringify(e)}`);
        }
      }
      if (infoMessageString.length > 0) {
        return {type: ResultType.success, message: infoMessageString};
      }
      return null
    }
  
    private static getRank(infoMsgCategory: string): number {
      switch (infoMsgCategory.toUpperCase()) {
        case FieldConstants.InfoMessageFailure:
        case FieldConstants.InfoMessageFailureAndProcess:
        case FieldConstants.InfoMessageMsgWithheld:
          return 1;
        case FieldConstants.InfoMessageWarning:
          return 2;
        case FieldConstants.InfoMessageInfo:
          return 3;
        case FieldConstants.InfoMessageSuccess:
          return 4;
        default:
          return 5;
      }
    }
  
    private static async checkAtleastOneBeAvailable(entityName: string, responseData: any, structureMeta: StructureMeta): Promise<boolean> {
      if (responseData[structureMeta.beName] != null) {
        let returnValue = false;
        const beValues = responseData[structureMeta.beName];
        for (const data of beValues) {
          if (data[entityName] != null) {
            returnValue = true;
            break;
          }
        }
        return returnValue;
      }
      return false;
    }
  
    static async checkDuplicateBe(tableName: string, fieldMetas: FieldMeta[], dataObj: any, requestType: RequestType, lid: string): Promise<any | null> {
        if (fieldMetas.length === 0) {
            return null;
        }
        let whereClaues = "";
        if (requestType !== RequestType.RQST) {
            whereClaues = ServerResponseHandler.getWhereClauseBasedOnGidFields(tableName, fieldMetas, dataObj);
        } else {
            if (tableName.endsWith("_HEADER") && lid.length > 0) {
                whereClaues = `${FieldConstants.FieldLid} = '${lid}'`;
            } else {
                whereClaues = ServerResponseHandler.getWhereClauseBasedOnGidFields(tableName, fieldMetas, dataObj);
                if (lid.length > 0) {
                    whereClaues += whereClaues.length === 0 ? "" : " AND ";
                    whereClaues += `${FieldConstants.FieldFid} = '${lid}'`;
                }
            }
        }

        const isPresent = await ServerResponseHandler.checkIfEntityPresentInDB(tableName, whereClaues);
        if (isPresent) {
            const databaseManager = DatabaseManager.getInstance();
            const result = await databaseManager.select(DatabaseType.AppDb, tableName, whereClaues);
            if (result.length > 0) {
                return result[0];
            }
        }
        return null;
    }
  
    private static async checkIfEntityPresentInDB(tableName: string, whereClause: string): Promise<boolean> {
      const result = await DatabaseManager.getInstance().count(DatabaseType.AppDb, tableName, whereClause);
      return result > 0;
    }
  
    private static getWhereClauseBasedOnGidFields(tableName: string, fieldMetas: FieldMeta[], dataObj: any): string {
        let whereClaues = "";
        var gidFields: string[] = [];
        try {
          gidFields = fieldMetas
              .filter((fMeta) => fMeta.structureName === tableName && fMeta.isGid === "1")
              .map((e) => e.fieldName);
        } catch (e) {
          Logger.logError(
              "ServerResponseHandler",
              "_getWhereClauseBasedOnGidFields",
              `Error while getting GID fields. Error: ${e}`);
        }
        for (const element of gidFields) {
          let val = dataObj[element];
          if (val != null) {
            if (typeof val === 'string') {
              val = DatabaseManager.getInstance().escapeSqlString(val);
            }
            whereClaues += whereClaues.length === 0 ? "" : " AND ";
            whereClaues += `${element} = '${val}'`;
          }
        }
        return whereClaues;
    }
  
    private static async insertIncomingBeData(isForeground: boolean, entityName: string, incomingHeader: any, incomingItems: any) {
      incomingHeader[FieldConstants.FieldTimestamp] = Date.now();
      incomingHeader[FieldConstants.FieldObjectStatus] = ObjectStatus.global;
      incomingHeader[FieldConstants.FieldSyncStatus] = SyncStatus.none;
      try {
        await DatabaseManager.getInstance().insert(DatabaseType.AppDb, entityName, incomingHeader, true);
      } catch (e) {
        const errorString =
          `Error while inserting incoming be - ${entityName}. Error: ${JSON.stringify(e)}`;
        Logger.logError(this.sourceClass, "insertIncomingBeData", errorString);
        const infoMessageData = await this.updateErrorStatusInHeaderAndCreateInfoMessage(
          isForeground,
          errorString,
          entityName,
          incomingHeader[FieldConstants.FieldLid] ?? "",
          FieldConstants.InfoMessageFailure
        );
        await DatabaseManager.getInstance().insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessageData, true);
        return;
      }
      const keys = Object.keys(incomingItems);
      for (const key of keys) {
        const itemsArray = incomingItems[key];
        let index = 0;
        for (const value of itemsArray) {
          if (!value[FieldConstants.FieldLid]) {
            value[FieldConstants.FieldLid] = FrameworkHelper.getUUID();
          }
          value[FieldConstants.FieldFid] = incomingHeader[FieldConstants.FieldLid];
          value[FieldConstants.FieldTimestamp] = Date.now();
          value[FieldConstants.FieldObjectStatus] = ObjectStatus.global;
          value[FieldConstants.FieldSyncStatus] = SyncStatus.none;
          Logger.logInfo(this.sourceClass, "insertIncomingBeData", `Inserting BE ITEM : ${key} LID : ${value[FieldConstants.FieldLid]} ${index.toString()}`);
          try {
            await DatabaseManager.getInstance().insert(DatabaseType.AppDb, key, value, false);
            if (key.endsWith(ServiceConstants.AttachmentBE)) {
                await AttachmentQHelper.checkAttachmentAndQueueForAutoDownload(key, value);
            }
          } catch (e) {
            const errorString =
              `Error while inserting incoming items. ERROR: ${JSON.stringify(e)}`;
            Logger.logError(this.sourceClass, "insertIncomingBeData", errorString);
            const infoMessageData = await this.updateErrorStatusInHeaderAndCreateInfoMessage(
              isForeground,
              
              errorString,
              entityName,
              incomingHeader[FieldConstants.FieldLid] ?? "",
              FieldConstants.InfoMessageFailure
            );
            await DatabaseManager.getInstance().insert(DatabaseType.FrameworkDb, "InfoMessage", infoMessageData, true);
            return;
          }
          index++;
        }
      }
    }
  
    static async updateErrorStatusInHeaderAndCreateInfoMessage(isForeground: boolean, errorString: string, entityName: string, headerLid: string, infoMsgCategory: string): Promise<any> {
        const infoMessage = {
            lid: FrameworkHelper.getUUID(),
            timestamp: Date.now(),
            objectStatus: ObjectStatus.global,
            syncStatus: SyncStatus.none,
            type: "",
            subtype: "",
            category: FieldConstants.InfoMessageFailure,
            message: errorString,
            bename: entityName,
            belid: headerLid,
            messagedetails: ""
        };
        if (headerLid.length === 0) {
            return infoMessage;
        }
        try {
            await DatabaseManager.getInstance().update(DatabaseType.AppDb, entityName, {
                [FieldConstants.FieldSyncStatus]: SyncStatus.error,
                [FieldConstants.FieldInfoMsgCat]: infoMsgCategory
            }, `${FieldConstants.FieldLid} = '${headerLid}'`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null.
        } catch (e) {
            Logger.logError(this.sourceClass, "updateErrorStatusInHeaderAndCreateInfoMessage", `Error while updating header (${entityName}) status into database. Error: ${JSON.stringify(e)}`);
        }

        const allStructureMetas = await DatabaseManager.getInstance().select(DatabaseType.FrameworkDb, "StructureMeta");
        const headerStructureMeta = allStructureMetas.find((element) => element.structureName === entityName);

        if (!headerStructureMeta) {
            Logger.logError(this.sourceClass, "updateErrorStatusInHeaderAndCreateInfoMessage", `Error while getting header structure meta. Error: Structure meta for ${entityName} is not found.`);
            return infoMessage;
        }

        try {
            const childStructureMetas = allStructureMetas.filter((element) => element.beName === headerStructureMeta.beName && element.isHeader !== "1");
            for (const childStructureMeta of childStructureMetas) {
                try {
                    await DatabaseManager.getInstance().update(DatabaseType.AppDb, childStructureMeta.structureName, {
                        [FieldConstants.FieldSyncStatus]: SyncStatus.none
                    }, `${FieldConstants.FieldFid} = '${headerLid}'`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null.
                } catch (e) {
                    Logger.logError(this.sourceClass, "updateErrorStatusInHeaderAndCreateInfoMessage", `Error while Updating Children Data Structure : ${childStructureMeta.structureName}. Error: ${JSON.stringify(e)}`);
                }
            }
        } catch (e) {
            Logger.logError(this.sourceClass, "updateErrorStatusInHeaderAndCreateInfoMessage", `Error while Fetching Children For Data Structure : ${entityName}. Error: ${JSON.stringify(e)}`);
        }
        return infoMessage;
    }
  
    private static async updateInfoMessageCategoryInHeader(isForeground: boolean, entityName: string, headerLid: string, infoMsgCategory: string) {
        if (!entityName || entityName.length === 0 || !headerLid || headerLid.length === 0) {
            return;
        }
        try {
            await DatabaseManager.getInstance().update(DatabaseType.AppDb, entityName, {
                [FieldConstants.FieldInfoMsgCat]: infoMsgCategory
            }, `${FieldConstants.FieldLid} = '${headerLid}'`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null.  
        } catch (e) {
            Logger.logError(this.sourceClass, "updateInfoMessageCategoryInHeader", `Error while updating header (${entityName}) Info Message Category into database. Error: ${JSON.stringify(e)}`);
        }
    }
}

export default ServerResponseHandler;