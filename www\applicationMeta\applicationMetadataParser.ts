import { ObjectStatus, SyncStatus } from '../helper/utils';
import FrameworkHelper from '../helper/frameworkHelper';
import * as FieldConstants from './fieldConstants';

export interface ApplicationMeta {
  lid: string;
  timestamp: number;
  objectStatus: number;
  syncStatus: number;
  appClassName: string;
  appId: string;
  appName: string;
  description: string;
  version: string;
  installationDate: string;
}

export interface BusinessEntityMeta {
  lid: string;
  timestamp: number;
  objectStatus: number;
  syncStatus: number;
  appName: string;
  beName: string;
  description: string;
  addFunction: string;
  modifyFunction: string;
  deleteFunction: string;
  notification: string;
  attachments: string;
  conflictRules: string;
  save: string;
}

export interface StructureMeta {
  lid: string;
  timestamp: number;
  objectStatus: number;
  syncStatus: number;
  structureName: string;
  description: string;
  className: string;
  isHeader: string;
  appName: string;
  beName: string;
}

export interface FieldMeta {
  lid: string;
  timestamp: number;
  objectStatus: number;
  syncStatus: number;
  appName: string;
  beName: string;
  structureName: string;
  fieldName: string;
  description: string;
  length: string;
  mandatory: string;
  sqlType: string;
  isGid: string;
}

export interface IndexMeta {
  indexName: string;
  structureName: string;
  fieldName: string[];
}

export default class ApplicationMetaParser {
  private applicationMeta!: ApplicationMeta;
  private businessEntityMetas: BusinessEntityMeta[] = [];
  private structureMetas: StructureMeta[] = [];
  private fieldMetas: FieldMeta[] = [];
  private indexMetas: IndexMeta[] = [];
  private metadataJSON: string = "";

  applicationName: string = "";
  applicationVersion: string = "";
  applicationNamespace: string = "";

  private static readonly _instance: ApplicationMetaParser = new ApplicationMetaParser();

  private constructor() {}

  public static get instance(): ApplicationMetaParser {
    return this._instance;
  }

  public async init(jsonString: string): Promise<void> {
    this.applicationName = "";
    this.businessEntityMetas = [];
    this.structureMetas = [];
    this.fieldMetas = [];
    this.indexMetas = [];
    this.metadataJSON = jsonString;

    await this.parse();
  }

  public getApplicationMeta(): ApplicationMeta {
    return this.applicationMeta;
  }

  public getBusinessEntityMetas(): BusinessEntityMeta[] {
    return this.businessEntityMetas;
  }

  public getStructureMetas(): StructureMeta[] {
    return this.structureMetas;
  }

  public getFieldMetas(): FieldMeta[] {
    return this.fieldMetas;
  }

  public getIndexMetas(): IndexMeta[] {
    return this.indexMetas;
  }

  public parseAppInfo(jsonString: string) {
    const jsonData:any = JSON.parse(jsonString);
    if (!jsonData) {
      return;
    }
    this.applicationName = jsonData[FieldConstants.ApplicationNameAttribute];
    this.applicationVersion = jsonData[FieldConstants.ApplicationVersionAttribute];
    this.applicationMeta = jsonData[FieldConstants.ApplicationNamespaceAttribute];
  }

  private async parse(): Promise<void> {
    const jsonData:any = JSON.parse(this.metadataJSON);
    if (!jsonData) {
      return;
    }
    this.applicationName = jsonData[FieldConstants.ApplicationNameAttribute];
    this.applicationVersion = jsonData[FieldConstants.ApplicationVersionAttribute];
    this.applicationMeta = jsonData[FieldConstants.ApplicationNamespaceAttribute];

    // Parse Application Meta
    const applicationDescription = jsonData[FieldConstants.ApplicationDescriptionAttribute] || "";
    const applicationClassName = jsonData[FieldConstants.ApplicationClassNameAttribute] || "";
    const currentDate = new Date();
    const formatter = new Intl.DateTimeFormat('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit',
    });

    this.applicationMeta = {
      lid: FrameworkHelper.getUUID(),
      timestamp: currentDate.getTime(),
      objectStatus: ObjectStatus.global,
      syncStatus: SyncStatus.none,
      appClassName: applicationClassName,
      appId: "",
      appName: this.applicationName,
      description: applicationDescription,
      version: this.applicationVersion,
      installationDate: formatter.format(currentDate),
    };

    const jsonDataKeys = Object.keys(jsonData);
    for (const key of jsonDataKeys) {
        const value = jsonData[key];
  
        if (key !== FieldConstants.ApplicationNameAttribute && key !== FieldConstants.ApplicationDescriptionAttribute && key !== FieldConstants.ApplicationVersionAttribute) {
          const beName = key;
          if (key === FieldConstants.IndexNode) {
            // Parse Index Meta
            for (const index of value as any[]) {
              const name = index[FieldConstants.IndexName];
              const structureName = index[FieldConstants.IndexTableName];
              const fieldNames = index[FieldConstants.IndexFields] as string[];
              const indexMeta: IndexMeta = {
                indexName: name,
                structureName: structureName,
                fieldName: fieldNames,
              };
              this.indexMetas.push(indexMeta);
            }
          } else if (!Array.isArray(value)) {
            // Parse Business Entity Meta
            const beDesc = value[FieldConstants.BeDescriptionAttribute] || "";
            const saveAttributeValue = value[FieldConstants.BeSaveAttribute] ?? false;
            const saveAttribute = saveAttributeValue ? "true" : "false";
  
            const conflictRule = value[FieldConstants.BeOnConflictAttribute] || FieldConstants.ConflictModeServerWins;
  
            const isAttachmentRequired = value[FieldConstants.BeAttachmentsAttribute] ?? false;
  
            const businessEntityMeta: BusinessEntityMeta = {
              lid: FrameworkHelper.getUUID(),
              timestamp: new Date().getTime(),
              objectStatus: ObjectStatus.global,
              syncStatus: SyncStatus.none,
              appName: this.applicationName,
              beName: beName,
              description: beDesc,
              addFunction: "",
              modifyFunction: "",
              deleteFunction: "",
              notification: "",
              attachments: isAttachmentRequired ? "1" : "0",
              conflictRules: conflictRule,
              save: saveAttribute,
            };
            this.businessEntityMetas.push(businessEntityMeta);
  
            // Parse Structure Meta
            const beJSON = value;
            const beJSONKeys = Object.keys(beJSON);
            for (const key2 of beJSONKeys) {
              const value2 = beJSON[key2];
              if (key2 !== FieldConstants.BeDescriptionAttribute &&
                  key2 !== FieldConstants.BeSaveAttribute &&
                  key2 !== FieldConstants.BeOnConflictAttribute &&
                  key2 !== FieldConstants.BeAttachmentsAttribute) {
                const structureName = key2;
  
                if (!Array.isArray(value2)) {
                  const structureDesc = value2[FieldConstants.BeItemDescriptionAttribute] || "";
                  const className = value2[FieldConstants.BeItemClassNameAttribute];
                  const isHeader = value2[FieldConstants.BeItemIsHeader] ?? false;
  
                  const structureMeta: StructureMeta = {
                    lid: FrameworkHelper.getUUID(),
                    timestamp: new Date().getTime(),
                    objectStatus: ObjectStatus.global,
                    syncStatus: SyncStatus.none,
                    structureName: structureName,
                    description: structureDesc,
                    className: className,
                    isHeader: isHeader ? "1" : "0",
                    appName: this.applicationName,
                    beName: beName,
                  };
  
                  this.structureMetas.push(structureMeta);
  
                  // Parse Field Meta
                  const fields = value2[FieldConstants.BeJsonFieldNode] as any[];
                  for (const field of fields) {
                    const name = field[FieldConstants.BeJsonFieldNameNode];
                    const description = field[FieldConstants.BeJsonFieldDescription] || "";
                    const fieldType = field[FieldConstants.BeJsonFieldSqlType];
                    const isGID = field[FieldConstants.BeJsonFieldIsGid] ?? false;
                    const isMandatory = field[FieldConstants.BeJsonFieldMandatory] ?? false;
  
                    let length = 0;
                    try {
                      length = parseInt(field[FieldConstants.BeJsonFieldLength] || "", 10);
                    } catch (e) {}
  
                    const fieldMeta: FieldMeta = {
                      lid: FrameworkHelper.getUUID(),
                      timestamp: new Date().getTime(),
                      objectStatus: ObjectStatus.global,
                      syncStatus: SyncStatus.none,
                      appName: this.applicationName,
                      beName: beName,
                      structureName: structureName,
                      fieldName: name,
                      description: description,
                      length: length.toString(),
                      mandatory: isMandatory ? "1" : "0",
                      sqlType: fieldType,
                      isGid: isGID ? "1" : "0",
                    };
                    this.fieldMetas.push(fieldMeta);
                }
              }
            }
          }
        }
      }
    }
  }
}


  