import { URLService } from './urlService';
import {isServerReachable, LoginParameters, LoginType} from './utils'
import * as ServiceConstants from './serviceConstants'
import PassCodeGenerator from './passcodeGenerator';
import FrameworkHelper from './frameworkHelper';
import { UserSettingsFields, UserSettingsManager } from './userSettingsManager';
import { FrameworkSettingsFields, FrameworkSettingsManager } from './frameworkSettingsManager';
import { Status } from './status';
import { AuthenticationService } from '../authenticationService';
import { UnviredAccountManager } from './unviredAccountManager';
import SettingsHelper from './settingsHelper';
import { Observable, throwError, catchError } from 'rxjs';
import { UnviredAccount } from './unviredAccount';
import { NotificationListenerHelper, NotificationListnerType } from './notificationListnerHelper';
import AttachmentHelper from '../attachment/attachmentHelper';

declare var Logger: any;

export class FetchAPIResponse {
  header: string | null;
  data: string | null;
  percentage: number | null;
  httpStatusCode: number;
  constructor(httpStatusCode: number, percentage: number | null = null, header: string | null = null, data: string | null = null) {
    this.header = header;
    this.data = data;
    this.percentage = percentage;
    this.httpStatusCode = httpStatusCode;
  }
}


const fileName = "HTTPConnection"

export let jwtAuthToken: string = "";
export const bearerAuth = (): string => jwtAuthToken.length === 0 ? "" : `Bearer ${jwtAuthToken}`;

export class HttpConnection {
  public sessionId = ""
  public async authenticateUser(loginParameters: LoginParameters): Promise<Response> {
    if (!navigator.onLine) {
        Logger.logDebug(fileName, "authenticateUser", "No Internet connection.");
        throw ("No Internet connection. Make sure your device is connected to the network and try again.");
    }

    if (!(await isServerReachable(loginParameters.url))) {
        Logger.logDebug(fileName, "authenticateUser", "Server not reachable.");
        throw ("Server not reachable. Make sure your device is connected to the valid network and try again.");
    }

    if (loginParameters.url == null || loginParameters.url.length == 0) {
        Logger.logError(fileName, "authenticateUser", "URL is empty.");
        throw ("URL is empty.");
    }
    Logger.logDebug(fileName, "authenticateUser", "Session REST api is called.");
    const authString = `${loginParameters.company}\\${loginParameters.username}:${loginParameters.password}`;

    if (loginParameters.loginType == LoginType.saml2) {
      jwtAuthToken = loginParameters["jwtToken"];
    }
    
    var basicAuth = (loginParameters.loginType == LoginType.saml2) ? bearerAuth() : 'Basic ' + btoa(authString);

    var baseUrl = URLService.getSessionUrl(loginParameters.url);
    if (loginParameters.loginType == LoginType.passwordless) {
        baseUrl = URLService.getBaseUrl(loginParameters.url);
        baseUrl += `${ServiceConstants.ServiceApplications}/${loginParameters.appName}/${ServiceConstants.ServicePasswordLess}`;
        if (jwtAuthToken.length > 0) {
          baseUrl += `/${jwtAuthToken}?startSession=true`;
          const url = new URL(baseUrl);
          const response = await fetch(url, {
            method: 'GET', // Set the HTTP method to POST
            headers: {
              'Authorization': basicAuth,
              'Access-Control-Allow-Origin': '*',
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
        });
        Logger.logDebug(fileName, "authenticateUser", `Session REST api response code: ${response.status}`);
        return response
      }
    }
    let body: any = {}
    if (loginParameters.loginType == LoginType.ads || loginParameters.loginType == LoginType.sap) {
        baseUrl += `/${ServiceConstants.ServiceApplications}/${loginParameters.appName}`;
        const url = new URL(baseUrl);
        body = {
            "credentials": JSON.stringify([
              {
                "port": `${loginParameters.port}`,
                "user": `${loginParameters.domain}\\${loginParameters.username}`,
                "password": `${loginParameters.password}`
              }
            ])
        };
    }
    const url = new URL(baseUrl);
    const response = await fetch(url, {
        method: 'POST', // Set the HTTP method to POST
        headers: {
          'Authorization': basicAuth,
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(body),
    });
    Logger.logDebug(fileName, "authenticateUser", `Session REST api response code: ${response.status}`);
    return response
  }

  public async activateUser(loginParameters: any): Promise<any> {
      if (!navigator.onLine) {
      Logger.logDebug(fileName,"activateUser","No internet connection.");
          throw new Error("No internet connection. Make sure your device is connected to the network and try again.");
      }

      if (!(await isServerReachable(loginParameters.url))) {
          Logger.logDebug(fileName, "activateUser", "Server not reachable.");
          throw ("Server not reachable. Make sure your device is connected to the valid network and try again.");
      }
      
      if (loginParameters.url == null || loginParameters.url.length == 0) {
      Logger.logError(fileName, "activateUser", "URL is empty.");
          throw new Error("URL is empty.");
      }
      if (loginParameters.loginType !== LoginType.saml2 && loginParameters.feUserId.length == 0) {
          Logger.logError(fileName, "activateUser", "Frontend is not set.");
          throw new Error("Frontend is not set.");
      }
      
      // Make activation call
      Logger.logDebug(fileName,"activateUser","Activation REST api is called.");
      let appUrl = URLService.getApplicationUrl(loginParameters.url);
      appUrl += `/${loginParameters.appName}/${ServiceConstants.ServiceActivate}`;
      if (loginParameters.loginType !== LoginType.saml2) {
          appUrl += `/${loginParameters.feUserId}`;
      } else {
          appUrl += "/saml";
          jwtAuthToken = loginParameters["jwtToken"];
      }
      let queryString = "";
      const postParams = await HttpConnection.getActivatePostParameters(loginParameters);
      Object.entries(postParams).forEach(([key, value]) => {
          queryString += queryString.length === 0 ? "?" : "&";
          queryString += `${key}=${value}`;
      });
      appUrl += queryString;
      
      if (loginParameters.loginType !== LoginType.saml2 && !this.sessionId.length) {
          Logger.logError(fileName, "activateUser", "Session is empty. User is not authenticated.");
          throw new Error("User is not authenticated.");
      }
      
      const userName = (loginParameters.loginType === LoginType.sap || loginParameters.loginType === LoginType.ads) ? loginParameters.unviredUserName : loginParameters.username;
      const authString = `${loginParameters.company}\\${userName}:${this.sessionId}`
      const basicAuth = loginParameters.loginType !== LoginType.saml2 ? 'Basic ' + btoa(authString) : bearerAuth();
      
      const url = new URL(appUrl);
      const activationResponse = await fetch(url, {
          method: "POST",
          headers: { authorization: basicAuth },
      });
      
      Logger.logDebug(fileName, "activateUser", `Activation REST api response code: ${activationResponse.status}`)
      return activationResponse;
  }

  public async getJwtToken(loginParameters: any): Promise<any> {
      if (!navigator.onLine) {
      Logger.logDebug(fileName,"getJwtToken","No internet connection.");
        throw new Error("No internet connection. Make sure your device is connected to the network and try again.");
      }
    
      if (!(await isServerReachable(loginParameters.url))) {
          Logger.logDebug(fileName, "getJwtToken", "Server not reachable.");
          throw ("Server not reachable. Make sure your device is connected to the valid network and try again.");
      }
    
      if (loginParameters.url == null || loginParameters.url.length == 0) {
      Logger.logError(fileName, "getJwtToken", "URL is empty.");
        throw new Error("URL is empty.");
      }
      if (loginParameters.feUserId == null || loginParameters.feUserId.length == 0) {
        Logger.logError(fileName, "activateUser", "Frontend is not set.");
        throw new Error("Frontend is not set.");
      }
    
      // Make get JWT Token call
      Logger.logDebug(fileName, "getJwtToken", "Get JWT Token REST api is called.");
    
      let unviredAccountPassword = loginParameters.password;
      if (!unviredAccountPassword) {
          unviredAccountPassword = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.unviredPassword);
      }
    
      const oneTimeToken = await this.getOnetimeTokenFromFWSettingsManager();
      const oneTimeTokenString = oneTimeToken ? `&${oneTimeToken}` : "";
    
      const appBaseUrl = URLService.getApplicationUrl(loginParameters.url);
      const appName = loginParameters.appName;
      const appUrl = `${appBaseUrl}/${appName}/${ServiceConstants.ServiceSession}?${ServiceConstants.QueryParamFrontendUser}=${loginParameters.feUserId}${oneTimeTokenString}`;
    
      var userName = loginParameters.username;
      if (loginParameters.loginType === LoginType.sap || loginParameters.loginType === LoginType.ads) {
        if (loginParameters.unviredUserName == null || loginParameters.unviredUserName.length === 0) {
          AuthenticationService.instance.loginParameters.unviredUserName = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.unviredUser);
          loginParameters.unviredUserName = AuthenticationService.instance.loginParameters.unviredUserName
        }
        userName = loginParameters.unviredUserName;
      }
    
      let password: string;
      switch (loginParameters.loginType) {
        case LoginType.passwordless:
          const md5UnviredPwd = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.md5Pwd);
          password = md5UnviredPwd != null && md5UnviredPwd.length > 0 ? md5UnviredPwd : unviredAccountPassword;
          break;
        default:
          password = unviredAccountPassword;
      }
    
      let basicAuth = "";
      let body: any = {};
      switch (loginParameters.loginType) {
        case LoginType.ads:
        case LoginType.sap:
          {
            const inp = `${loginParameters.company}\\${loginParameters.domain}\\${userName}:${password}`;
            basicAuth = 'Basic ' + btoa(inp);
            body = {
              credentials: JSON.stringify([
                {
                  port: `${loginParameters.port}`,
                  user: `${loginParameters.domain}\\${userName}`,
                  password: `${password}`,
                },
              ]),
            };
          }
          break;
        case LoginType.saml2:
          {
            basicAuth = bearerAuth();
          }
          break;
        default:
          {
            basicAuth = 'Basic ' + btoa(`${loginParameters.company}\\${userName}:${password}`);
          }
      }
    
      const url = new URL(appUrl);
      const jwtResponse = await fetch(url, {
        method: "POST",
        headers: {
          authorization: basicAuth,
          Accept: "application/json",
        },
        body: JSON.stringify(body),
      });
      const tempJwtResponse = jwtResponse.clone();
      Logger.logDebug(fileName, "getJwtToken", `Activation REST api response code: ${jwtResponse.status}`);
      const jwtResponseObject = JSON.parse(await tempJwtResponse.text());
      if (jwtResponse.status === Status.created) {
        jwtAuthToken = jwtResponseObject[ServiceConstants.KeyToken];
        await this.updateJwtToken(jwtAuthToken);
      } else if (jwtResponse.status === Status.unauthorized) {
        NotificationListenerHelper.postDataSenderNotification(null, NotificationListnerType.jwtTokenExpired);
        // TODO: Need to check if the clear data is required.
        AuthenticationService.instance.clearData();
      }
      return jwtResponse;
  }
    
  public async getJSONFromFile(filePath: string): Promise<string> {
    try {
      const response = await fetch(filePath.replace("./assets/", "assets/"), {
        method: 'GET', // Set the HTTP method to POST
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      const data = await response.json();
      return JSON.stringify(data);
    } catch (e) {
      Logger.logError(fileName, "getJSONFromFile", `Error fetching file: ${e}`);
      return "";
    }
  }

  public makeSyncCallWithObservable(bodyData: string, paName: string): Observable<FetchAPIResponse> {
    return new Observable(observer => {
      async () => {
        let account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
        let appName = AuthenticationService.instance.loginParameters.appName;

        if (!account) {
          await Logger.logError(fileName, "makeSyncCallWithObservable", "Account data not available.");
          observer.error("Account data not available.");
          return;
        }

        if (!jwtAuthToken) {
            await Logger.logInfo(fileName, "makeSyncCallWithObservable", "Reading JWT token from database...");
            jwtAuthToken = account.getJwtToken();
        } 

        // if (!jwtAuthToken) {
        //     await Logger.logError(fileName, "makeSyncCallWithObservable", "token not available. Refreshing token...");
        //     await this.getJwtToken(AuthenticationService.instance.loginParameters);
        // }

        if (!jwtAuthToken) {
            await Logger.logError(fileName, "makeSyncCallWithObservable", "Token not available.");
            observer.error("Token not available.");
            return;
        }

        const appBaseUrl = URLService.getApplicationUrl(AuthenticationService.instance.loginParameters.url);
        const appUrl = `${appBaseUrl}/${appName}/${ServiceConstants.ServiceExecute}/${paName}`;
        const url = new URL(appUrl);
        // const inputObj = JSON.parse(bodyData);
        const headers: HeadersInit = {
            'Authorization': bearerAuth(),
            'Content-Type': "application/x-www-form-urlencoded",
            "Accept": "text/event-stream"
        };
        this.downloadWithStream(url.toString(), bodyData, headers).subscribe(async(response: FetchAPIResponse) => {
          if (response.httpStatusCode === Status.unauthorized) {
            NotificationListenerHelper.postDataSenderNotification(null, NotificationListnerType.jwtTokenExpired);
            // AuthenticationService.instance.loginParameters.password = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.md5Pwd);
            // await this.getJwtToken(AuthenticationService.instance.loginParameters);
            // if (jwtAuthToken) {
            //     this.makeSyncCallWithObservable(bodyData, paName).subscribe(async(response: FetchAPIResponse) => {
            //       observer.next(response);
            //       observer.complete();
            //     });
            // }
          }
          observer.next(response);
          Logger.logInfo("HTTPConnection", "makeSyncCallWithObservable", `Event percentage: ${response.percentage}`);
          if (response.percentage == null || response.percentage == 100) {
            Logger.logDebug("HTTPConnection", "makeSyncCall", `Event header: ${response.header}`);
            Logger.logDebug("HTTPConnection", "makeSyncCall", `Event data: ${response.data}`);
            const headers = JSON.parse(response.header);
            const jwtToken = headers[ServiceConstants.KeyJwtToken];
            await this.updateJwtToken(jwtToken);
            observer.complete();
          }
        });
      }
    })
  }

  public async makeSyncCall(bodyData: string, paName: string, account: UnviredAccount, loginParameters: any): Promise<FetchAPIResponse> {
    return new Promise(async (resolve, reject) => {
      let appName = loginParameters.appName;

      if (!jwtAuthToken) {
        await Logger.logInfo(fileName, "makeSyncCall", "Reading JWT token from database...");
        jwtAuthToken = account.getJwtToken();
      }

      // if (!jwtAuthToken) {
      //     await Logger.logError(fileName, "makeSyncCall", "token not available. Refreshing token...");
      //     await this.getJwtToken(AuthenticationService.instance.loginParameters);
      // }

      if (!jwtAuthToken) {
          await Logger.logError(fileName, "makeSyncCall", "Token not available.");
          reject("Token not available.");
          return;
      }

      if (!account) {
          await Logger.logError(fileName, "makeSyncCall", "Account data not available.");
          reject("Account data not available.");
          return;
      }

      const appBaseUrl = URLService.getApplicationUrl(AuthenticationService.instance.loginParameters.url);
      const appUrl = `${appBaseUrl}/${appName}/${ServiceConstants.ServiceExecute}/${paName}`;
      const url = new URL(appUrl);
      const inputObj = JSON.parse(bodyData);

      const urlencodedBody = new URLSearchParams(inputObj).toString();
      const headers: HeadersInit = {
          'Authorization': bearerAuth(),
          'Content-Type': "application/x-www-form-urlencoded",
          "Accept": "application/json"
      };

      // GZip compression
      // const frameworkSettingsManager = FrameworkSettingsManager.getInstance();
      // const compressPostData = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.compressPostData);
      // if (compressPostData === 'yes') {
      //     headers['Content-Encoding'] = "gzip";
      // }

      // this.downloadWithStream(url.toString(), JSON.stringify(inputObj), headers).subscribe(async(response: FetchAPIResponse) => {
      //   if (response.httpStatusCode === Status.unauthorized) {
      //     NotificationListenerHelper.postDataSenderNotification(null, NotificationListnerType.jwtTokenExpired);
          // AuthenticationService.instance.loginParameters.password = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.md5Pwd);
          // await this.getJwtToken(AuthenticationService.instance.loginParameters);
          // if (jwtAuthToken) {
          //     const resp = await this.makeSyncCall(bodyData, paName);
          //     resolve(resp)
          //     return;
          // }
      //   }
      //   Logger.logInfo("HTTPConnection", "makeSyncCall", `Event percentage: ${response.percentage}`);
      //   if (response.percentage == null || response.percentage == 100) {
      //     Logger.logDebug("HTTPConnection", "makeSyncCall", `Event header: ${response.header}`);
      //     Logger.logDebug("HTTPConnection", "makeSyncCall", `Event data: ${response.data}`);
      //     const headers = JSON.parse(response.header);
      //     const jwtToken = headers[ServiceConstants.KeyJwtToken];
      //     await this.updateJwtToken(jwtToken);
      //     resolve(response);
      //     return;
      //   }
      // });

      const timeoutInSeconds = (await SettingsHelper.getRequestTimeout()) * 60 || ServiceConstants.DefaultHttpTimeout;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);

      try {
        const syncResponse = await fetch(url.toString(), {
          method: 'POST',
          headers: headers,
          body: urlencodedBody,
          signal: controller.signal
        });
        clearTimeout(timeoutId);
        const jwtToken = syncResponse.headers.get(ServiceConstants.KeyJwtToken);
        await this.updateJwtToken(jwtToken);
        if (syncResponse.status === Status.unauthorized) {
          NotificationListenerHelper.postDataSenderNotification(null, NotificationListnerType.jwtTokenExpired);
          // AuthenticationService.instance.loginParameters.password = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.md5Pwd);
          // await this.getJwtToken(AuthenticationService.instance.loginParameters);
          // if (jwtAuthToken) {
          //     return await this.makeSyncCall(bodyData, paName, account, loginParameters);
          // }
        }
        let dataObj = {}
        if (syncResponse.status != Status.noContent) {
          dataObj = await syncResponse.json()
        }
        const headerJson:any = {}
        syncResponse.headers.forEach((value, key) => {
          headerJson[key] = value;
        });
        const response = new FetchAPIResponse(syncResponse.status, 100, JSON.stringify(headerJson), JSON.stringify(dataObj));
        resolve(response);
      } catch (error) {
        if (error.name === 'AbortError') {
            await Logger.logError("HTTPConnection", "makeSyncCall", "Request timed out. Error: " + error);
        } else {
            await Logger.logError("HTTPConnection", "makeSyncCall", "Error while making sync call. Error: " + error);
        }
        reject(error);
      }
    });
  }

  public async uploadAttachment(attachmentItem: any): Promise<Response> {
    const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
    if (!account) {
        Logger.logError("HTTPConnection", "uploadAttachment", "Account data not available.");
        throw new Error("Account data not available.");
    }

    if (!jwtAuthToken) {
      await Logger.logInfo(fileName, "uploadAttachment", "Reading JWT token from database...");
      jwtAuthToken = account.getJwtToken();
    }

    // if (!jwtAuthToken) {
    //     Logger.logError("HTTPConnection", "uploadAttachment", "Token not available. Refreshing token...");
    //     await this.getJwtToken(AuthenticationService.instance.loginParameters);
    // }

    if (!jwtAuthToken) {
        Logger.logError("HTTPConnection", "uploadAttachment", "Token not available.");
        throw new Error("Token not available.");
    }

    const gUid = attachmentItem[ServiceConstants.AttachmentItemFieldUid];
    const filePath = `${attachmentItem[ServiceConstants.AttachmentItemFieldLocalPath]}`;

    const appBaseUrl = URLService.getApplicationUrl(account.getServerURL());
    const appUrl = `${appBaseUrl}/${AuthenticationService.instance.loginParameters.appName}/${ServiceConstants.ServiceAttachments}/${gUid}`;
    const url = new URL(appUrl);

    let attachmentFileName = attachmentItem[ServiceConstants.AttachmentItemFieldFileName];
    if (!attachmentFileName) {
      attachmentFileName = filePath.split("/").pop()!;
    }
    console.log("~~~attachmentFileName", attachmentFileName);
    let attachmentData = null;
    try {
      attachmentData = await AttachmentHelper.getAttachment(attachmentFileName)
      Logger.logDebug("HTTPConnection", "uploadAttachment", `Attachment data size: ${attachmentData?.byteLength || 0} bytes`);
    } catch (error) {
      Logger.logError("HTTPConnection", "uploadAttachment", `Error getting attachment: ${JSON.stringify(error)}`);
      throw error;
    }

    // Create blob from attachment data
    const blob = new Blob([attachmentData], { type: 'application/octet-stream' });
    Logger.logDebug("HTTPConnection", "uploadAttachment", `Blob size: ${blob.size} bytes, type: ${blob.type}`);
    
    // Create FormData with proper multipart handling
    const formData = new FormData();
    formData.append("file", blob, attachmentFileName);  // Use blob directly with filename

    const headers: HeadersInit = {
        'Authorization': bearerAuth()
    };

    const timeoutInSeconds = (await SettingsHelper.getRequestTimeout()) * 60 || ServiceConstants.DefaultHttpTimeout;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);

    try {
        const response = await fetch(url.toString(), {
            method: 'POST',
            headers: headers,
            body: formData,
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        
        Logger.logDebug("HTTPConnection", "uploadAttachment", `Response status: ${response.status}`);
        const responseHeaders: Record<string, string> = {};
        response.headers.forEach((value, key) => {
            responseHeaders[key] = value;
        });
        Logger.logDebug("HTTPConnection", "uploadAttachment", `Response headers: ${JSON.stringify(responseHeaders)}`);
        
        if (!response.ok) {
            const errorText = await response.text();
            Logger.logError("HTTPConnection", "uploadAttachment", `Error response: ${errorText}`);
            throw new Error(`Upload failed with status ${response.status}: ${errorText}`);
        }
        return response;
    } catch (error) {
        if (error.name === 'AbortError') {
            Logger.logError("HTTPConnection", "uploadAttachment", "Request timed out. Error: " + JSON.stringify(error));
        } else {
            Logger.logError("HTTPConnection", "uploadAttachment", `Error: ${JSON.stringify(error)}`);
        }
        throw error;
    }
  }

  public static async makeAdminServicesCall(inputData: string, paName: string, account: UnviredAccount, loginParameters: any): Promise<Response> {
    await Logger.logInfo("HTTPConnection", "makeAdminServicesCall", "Calling admin services...");
    
    if (!navigator.onLine) {
      await Logger.logInfo("HTTPConnection", "makeAdminServicesCall", "No Internet connection.");
      throw new Error("No Internet connection. Make sure your device is connected to the network and try again.");
    }
  
    if (account == null) {
      await Logger.logError("HTTPConnection", "makeAdminServicesCall", "No last logged in account found.");
      throw new Error("Account data not available.");
    }
    let appName = loginParameters.appName

    if (!jwtAuthToken) {
      await Logger.logInfo(fileName, "makeAdminServicesCall", "Reading JWT token from database...");
      jwtAuthToken = account.getJwtToken();
    }

    // if (!jwtAuthToken) {
    //   await Logger.logError("HTTPConnection", "makeAdminServicesCall", "Token not available. Refreshing token...");
    //   await new HttpConnection().getJwtToken(loginParameters);
    // }
  
    if (!jwtAuthToken) {
      await Logger.logError("HTTPConnection", "makeAdminServicesCall", "Token not available.");
      throw new Error("Token not available.");
    }
  
    const appBaseUrl = URLService.getApplicationUrl(account.getServerURL());
    let queryString = "";
    const postParams = await HttpConnection.getCommonPostParameters();
    for (const [k, v] of Object.entries(postParams)) {
      queryString += (queryString.length > 0 ? "&" : "") + `${k}=${v}`;
    }
  
    const appUrl = `${appBaseUrl}/${appName}/${ServiceConstants.ServiceAdminServices}/${paName}?${queryString}`;
    const url = new URL(appUrl);
    const inputObj = JSON.parse(inputData);
    inputObj[ServiceConstants.QueryParamFrontendUser] = account.getFeUserId();
  
    const timeoutInSeconds = (await SettingsHelper.getRequestTimeout()) * 60 || ServiceConstants.DefaultHttpTimeout;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);
    const urlencodedBody = new URLSearchParams(inputObj).toString();
    try {
      const syncResponse = await fetch(url.toString(), {
        method: 'POST',
        headers: {
          'Authorization': bearerAuth(),
          'Content-Type': "application/x-www-form-urlencoded",
          "Accept": "application/json"
        },
        body: urlencodedBody,
        signal: controller.signal
      });
  
      clearTimeout(timeoutId);
  
      const jwtToken = syncResponse.headers.get(ServiceConstants.KeyJwtToken);
      await (new HttpConnection()).updateJwtToken(jwtToken);
  
      return syncResponse;
    } catch (error) {
      if (error.name === 'AbortError') {
        await Logger.logError("HTTPConnection", "makeAdminServicesCall", "Request timed out. Error: " + JSON.stringify(error));
      } else {
        await Logger.logError("HTTPConnection", "makeAdminServicesCall", "Error while pinging to server. Error: " + JSON.stringify(error));
      }
      throw error;
    }
  }

  public async downloadMessage(account: UnviredAccount, loginParameters: any): Promise<FetchAPIResponse> {
    return new Promise(async (resolve, reject) => {
      let appName = loginParameters.appName;
      if (!account) {
        await Logger.logError(fileName, "downloadMessage", "Account data not available.");
        reject("Account data not available.");
        return;
      }

      if (!jwtAuthToken) {
        await Logger.logInfo(fileName, "downloadMessage", "Reading JWT token from database...");
        jwtAuthToken = account.getJwtToken();
      }

      // if (!jwtAuthToken) {
      //     await Logger.logError(fileName, "downloadMessage", "token not available. Refreshing token...");
      //     await this.getJwtToken(AuthenticationService.instance.loginParameters);
      // }

      if (!jwtAuthToken) {
          await Logger.logError(fileName, "downloadMessage", "Token not available.");
          reject("Token not available.");
          return;
      }


      const appBaseUrl = URLService.getApplicationUrl(AuthenticationService.instance.loginParameters.url);
      const oneTimeToken = await this.getOnetimeTokenFromFWSettingsManager();
      const tokenQuery = oneTimeToken ? `?${oneTimeToken}` : '';
      const appUrl = `${appBaseUrl}/${appName}/${ServiceConstants.ServiceMessage}/${account.getFeUserId()}${tokenQuery}`;
      const url = new URL(appUrl);
      const headers: HeadersInit = {
          'Authorization': bearerAuth(),
          'Content-Type': "application/x-www-form-urlencoded",
          "Accept": "application/json"
      };

      const timeoutInSeconds = (await SettingsHelper.getRequestTimeout()) * 60 || ServiceConstants.DefaultHttpTimeout;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);

      try {
        const downloadResponse = await fetch(url.toString(), {
          method: 'GET',
          headers: headers,
          signal: controller.signal
        });
        clearTimeout(timeoutId);
        const jwtToken = downloadResponse.headers.get(ServiceConstants.KeyJwtToken);
        await this.updateJwtToken(jwtToken);
        if (downloadResponse.status === Status.unauthorized) {
          NotificationListenerHelper.postDataSenderNotification(null, NotificationListnerType.jwtTokenExpired);
          // AuthenticationService.instance.loginParameters.password = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.md5Pwd);
          // await this.getJwtToken(AuthenticationService.instance.loginParameters);
          // if (jwtAuthToken) {
          //     return await this.downloadMessage(account, loginParameters);
          // }
        }
        var dataObj:any = {}
        const headerJson:any = {}
        downloadResponse.headers.forEach((value, key) => {
          headerJson[key] = value;
        });
        if (downloadResponse.status != Status.noContent) {
          dataObj = await downloadResponse.json()
        }
        const response = new FetchAPIResponse(downloadResponse.status, 100, JSON.stringify(headerJson), JSON.stringify(dataObj));
        resolve(response);
      } catch (error) {
        if (error.name === 'AbortError') {
            await Logger.logError("HTTPConnection", "makeSyncCall", "Request timed out. Error: " + JSON.stringify(error));
        } else {
            await Logger.logError("HTTPConnection", "makeSyncCall", "Error while downloading messages from server. Error: " + JSON.stringify(error));
        }
        reject(error);
      }
    });
  }

  public async acknowledgeMessageService(convId: string): Promise<Response> {
    
    if (!navigator.onLine) {
      await Logger.logDebug(fileName, "acknowledgeMessageService", "No Internet connection.");
      throw new Error("No Internet connection. Make sure your device is connected to the network and try again.");
    }

    const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
    let appName = AuthenticationService.instance.loginParameters.appName;
    
    if (!account) {
      await Logger.logError(fileName, "acknowledgeMessageService", "Account data not available.");
      throw new Error("Account data not available.");
    }

    if (!account.getServerURL()) {
      await Logger.logError(fileName, "acknowledgeMessageService", "URL is empty.");
      throw new Error("URL is empty.");
    }
  
    if (!jwtAuthToken) {
      await Logger.logInfo(fileName, "acknowledgeMessageService", "Reading JWT token from database...");
      jwtAuthToken = account.getJwtToken();
    }
    
    // if (!jwtAuthToken) {
    //   await Logger.logError(fileName, "acknowledgeMessageService",
    //     "Token not available. Refreshing token...");
    //   await this.getJwtToken(AuthenticationService.instance.loginParameters);
    // }
  
    if (!jwtAuthToken) {
      await Logger.logError(fileName, "acknowledgeMessageService",
        "Token not available.");
      throw new Error("Token not available.");
    }
  
    const oneTimeToken = await this.getOnetimeTokenFromFWSettingsManager();
    const tokenQuery = oneTimeToken ? `?${oneTimeToken}` : '';
    const appBaseUrl = URLService.getBaseUrl(account.getServerURL());
    const appUrl =
      `${appBaseUrl}${ServiceConstants.ServiceMessages}/${ServiceConstants.ServiceFrontendUsers}/${account.getFeUserId()}/${ServiceConstants.ServiceConversation}/${convId}${tokenQuery}`;
  
    await Logger.logDebug(fileName, "acknowledgeMessageService",
      "Acknowledge message api is called.");
  

    const timeoutInSeconds = (await SettingsHelper.getRequestTimeout()) * 60 || ServiceConstants.DefaultHttpTimeout;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);
  
    try {
  
      const response = await fetch(appUrl, {
        method: 'DELETE',
        headers: {
          'authorization': bearerAuth()
        },
        signal: controller.signal
      });
  
      clearTimeout(timeoutId);
  
      await Logger.logDebug(fileName, "acknowledgeMessageService",
        `Acknowledge message api response code: ${response.status}`);
  
      return response;
    } catch (error) {
      await Logger.logError(fileName, "acknowledgeMessageService",
          `Error: ${JSON.stringify(error)}`);
      throw error;
    }
  }

  public async registerNotification(pushToken: string): Promise<Response> {
    
    if (!navigator.onLine) {
      await Logger.logDebug(fileName, "registerNotification", "No Internet connection.");
      throw new Error("No Internet connection. Make sure your device is connected to the network and try again.");
    }

    const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
    let appName = AuthenticationService.instance.loginParameters.appName;
    
    if (!account) {
      await Logger.logError(fileName, "registerNotification", "Account data not available.");
      throw new Error("Account data not available.");
    }

    if (!account.getServerURL()) {
      await Logger.logError(fileName, "registerNotification", "URL is empty.");
      throw new Error("URL is empty.");
    }
  
    if (!jwtAuthToken) {
      await Logger.logInfo(fileName, "registerNotification", "Reading JWT token from database...");
      jwtAuthToken = account.getJwtToken();
    }
    
    // if (!jwtAuthToken) {
    //   await Logger.logError(fileName, "acknowledgeMessageService",
    //     "Token not available. Refreshing token...");
    //   await this.getJwtToken(AuthenticationService.instance.loginParameters);
    // }
  
    if (!jwtAuthToken) {
      await Logger.logError(fileName, "registerNotification",
        "Token not available.");
      throw new Error("Token not available.");
    }
  
    const oneTimeToken = await this.getOnetimeTokenFromFWSettingsManager();
    const tokenQuery = oneTimeToken ? `?${oneTimeToken}` : '';
    const appBaseUrl = URLService.getBaseUrl(account.getServerURL());
    let queryString = "";
    const postParams = await HttpConnection.getCommonPostParameters();
    for (const [k, v] of Object.entries(postParams)) {
      queryString += (queryString.length > 0 ? "&" : "") + `${k}=${v}`;
    }
    const appUrl =
      `${appBaseUrl}applications/${appName}/${ServiceConstants.ServiceFrontendUsers}/${account.getFeUserId()}/registerforpush/${pushToken}?${queryString}`;
  
    await Logger.logDebug(fileName, "registerNotification",
      "Register notification api is called.");
  

    const timeoutInSeconds = (await SettingsHelper.getRequestTimeout()) * 60 || ServiceConstants.DefaultHttpTimeout;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);
  
    try {
  
      const response = await fetch(appUrl, {
        method: 'POST',
        headers: {
          'authorization': bearerAuth()
        },
        signal: controller.signal
      });
  
      clearTimeout(timeoutId);
      const jwtToken = response.headers.get(ServiceConstants.KeyJwtToken);
      await this.updateJwtToken(jwtToken);
  
      await Logger.logDebug(fileName, "registerNotification",
        `Register notification api response code: ${response.status}`);
  
      return response;
    } catch (error) {
      await Logger.logError(fileName, "registerNotification",
          `Error: ${JSON.stringify(error)}`);
      throw error;
    }
  }


  
  public async downloadAttachment(attachmentUid: string): Promise<Response> {
    await Logger.logDebug("HTTPConnection", "downloadAttachment",
      `Processing UID : ${attachmentUid}`);

    const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
    let appName = AuthenticationService.instance.loginParameters.appName;

    if (account === null) {
      await Logger.logError("HTTPConnection", "downloadAttachment",
        "Account data not available.");
      throw new Error("Account data not available.");
    }
    
    if (!jwtAuthToken) {
      await Logger.logInfo(fileName, "downloadAttachment", "Reading JWT token from database...");
      jwtAuthToken = account.getJwtToken();
    }

    // if (!jwtAuthToken) {
    //   await Logger.logError("HTTPConnection", "downloadAttachment",
    //     "Token not available. Refreshing token...");
    //   await this.getJwtToken(AuthenticationService.instance.loginParameters);
    // }
  
    if (!jwtAuthToken) {
      await Logger.logError("HTTPConnection", "downloadAttachment", "Token not available.");
      throw new Error("Token not available.");
    }
    const appBaseUrl = URLService.getApplicationUrl(account.getServerURL());
    const appUrl = `${appBaseUrl}/${appName}/${ServiceConstants.ServiceAttachments}/${attachmentUid}`;
    await Logger.logInfo("HTTPConnection", "downloadAttachment", `downloading URL : ${appUrl}`);
    const url = new URL(appUrl);
  
    const timeoutInSeconds = (await SettingsHelper.getRequestTimeout()) * 60 || ServiceConstants.DefaultHttpTimeout;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);
  
    const response = await fetch(url, {
      headers: {
        'authorization': bearerAuth()
      },
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  }
  
  static async getCommonPostParameters(): Promise<any> {
    const frameworkSettingsManager = FrameworkSettingsManager.getInstance();
    let deviceModel = window.device.model;
    let deviceOsVersion = window.device.version;
    let appVersion = AuthenticationService.instance.loginParameters.appVersion;
    
    // TODO:
    // const outboxCount = await Outbox.outboxCount();
    // const sentItemsCount = await Outbox.sentItemsCount();
    // const inboxCount = await Inbox.inboxCount();

    const activationId = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.activationId);
    const companyAlias = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.namespace);
    const serverId = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.serverId);
    const feUserId = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.feUser);
    const oneTimeToken = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.oneTimeToken);
    const deviceType = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.frontendType);

    const userId = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.unviredUserId);
    let password = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.unviredPassword);
    const loginType = AuthenticationService.instance.loginParameters.loginType;

    if ((loginType === LoginType.unvired || loginType === LoginType.email) && password) {
        // Additional logic if needed
    }

    const params: { [key: string]: string } = {};
    if (FrameworkHelper.getPlatform() != "browser") {
      try {
        const passCodeAndTime = PassCodeGenerator.computePin(oneTimeToken, feUserId);
        if (password == undefined || password == null || password == "") {
          password = AuthenticationService.instance.loginParameters.password;
        }
        password = FrameworkHelper.getMD5String(password + passCodeAndTime[1]);
        params[ServiceConstants.ParamOneTimeToken] = passCodeAndTime[0];
        params[ServiceConstants.ParamMessageTime] = passCodeAndTime[1];
      } catch (e) {
        Logger.logError("HTTPConnection", "getCommonPostParameters", "Error while generating pin. Error: " + JSON.stringify(e));
      }
    }

    params[ServiceConstants.ParamServerUserId] = userId;
    params[ServiceConstants.ParamPassword] = password;
    params[ServiceConstants.ParamApplication] = AuthenticationService.instance.loginParameters.appName;
    params[ServiceConstants.ParamFrameworkVersion] = ServiceConstants.FrameworkVersionNumber;
    params[ServiceConstants.ParamAppVersion] = appVersion;
    params[ServiceConstants.ParamDeviceOsVersion] = deviceOsVersion;
    params[ServiceConstants.ParamDeviceModel] = deviceModel;
    params[ServiceConstants.ParamFeUserId] = feUserId;
    params[ServiceConstants.ParamServerId] = serverId;
    params[ServiceConstants.ParamDeviceType] = deviceType;
    params[ServiceConstants.ParamCompanyAlias] = companyAlias;
    params[ServiceConstants.ParamLoginType] = loginType;

    return params;
}

  static async getActivatePostParameters(loginParameters: any): Promise<any> {
    let deviceModel = window.device.model;
    let deviceOsVersion = window.device.version;
    let appVersion = "";
    
    // TODO:
    // try {
    //   appVersion = await SettingsHelper().getApplicationVersionNumber();
    // } catch (e) {}
  
    const params: any = {}
    params[ServiceConstants.ParamApplication] = loginParameters.appName,
    params[ServiceConstants.ParamCompanyAlias] = loginParameters.company,
    params[ServiceConstants.ParamCompanyNamespace] = loginParameters.company,
    params[ServiceConstants.ParamFrameworkVersion] = ServiceConstants.FrameworkVersionNumber,
    params[ServiceConstants.ParamAppVersion] = AuthenticationService.instance.loginParameters.appVersion,
    params[ServiceConstants.ParamDeviceOsVersion] = deviceOsVersion,
    params[ServiceConstants.ParamDeviceModel] = deviceModel

    return params;
  }

  async getOnetimeTokenFromFWSettingsManager(): Promise<string> {
    try {
      if (FrameworkHelper.getPlatform() != "unknown") {
        var frameworkSettingsManager = FrameworkSettingsManager.getInstance();
        const oneTimeToken = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.oneTimeToken);
        const feUserId = await frameworkSettingsManager.getFieldValue(FrameworkSettingsFields.feUser);
        if (!oneTimeToken.length || !feUserId.length) {
          return "";
        }
        const passCodeAndTime = PassCodeGenerator.computePin(oneTimeToken, feUserId);
        return `${ServiceConstants.ParamOneTimeToken}=${passCodeAndTime[0]}&${ServiceConstants.ParamMessageTime}=${passCodeAndTime[1]}`;
      }
    } catch (e) {
      Logger.logError("HTTPConnection", "getOnetimeTokenFromFWSettingsManager", "Error: " + JSON.stringify(e));
    }
    return "";
  }
  
  downloadWithStream(url: string, body: string, headers: HeadersInit): Observable<FetchAPIResponse> {
    headers["Accept"] = 'text/event-stream'
    return new Observable(observer => {
      const fetchData = async () => {
        let receivedLength = 0; // bytes received that have been confirmed written to disk

        try {
          const timeoutInSeconds = (await SettingsHelper.getRequestTimeout()) * 60 || ServiceConstants.DefaultHttpTimeout;
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);
          const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: body,
            mode: 'cors',
            signal: controller.signal
          });
          clearTimeout(timeoutId);
          const reader = response.body?.getReader();
          if (!reader) {
            observer.next(new FetchAPIResponse(response.status))
            observer.complete()
            return;
          }

          let chunks = []; // array to collect chunks
          let isHeaderReceived = false;
          let totalLength = 0;

          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              break;
            }
            chunks.push(value);

            // Check whether we have received the headers.
            if (!isHeaderReceived) {
              let headerString = await this.getHeaderFromChunks(chunks);
              let header = JSON.parse(headerString)
              if (header) {
                isHeaderReceived = true;
                receivedLength = -value.length; // To make the first percentage a zero percentage.
                totalLength = header['Content-Length'];
              }
            }

            if (isHeaderReceived) {
              receivedLength += value.length;
              const percentage = (receivedLength / totalLength) * 100;

              // We send the percentage value as 100 when the data is fully received
              // till then, we do not want to send anything more than 100 (this can happen because of extra bytes due to escape characters in the response)
              if (percentage < 100) {
                observer.next(new FetchAPIResponse(response.status, percentage));
              }
            }
          }

          // Data download is complete.
          // Now we need to get the header and data from the chunks.

          let headerString = await this.getHeaderFromChunks(chunks);
          let dataString = await this.getDataFromChunks(chunks);

          observer.next(new FetchAPIResponse(response.status, 100, headerString, dataString));
          observer.complete();

        } catch (error) {
          console.error('Failed to fetch data', error);
          observer.error(error);
        }
      };
      fetchData();
    });
  }

  private getDataFromChunks(chunks: Uint8Array[]): Promise<string> {
    return this.getObjectFromChunks(chunks, 1)
  }

  private getHeaderFromChunks(chunks: Uint8Array[]): Promise<string> {
    return this.getObjectFromChunks(chunks, 0)
  }

  private getObjectFromChunks(chunks: Uint8Array[], objectIndex: number): Promise<string> {
    return new Promise((resolve, reject) => {
      const blob = new Blob(chunks);

      // Get string from blob
      const reader = new FileReader();
      reader.readAsText(blob);

      let headerString = ''
      reader.onload = () => {
        let headerAndData = (reader.result as string).split('\\n');
        if (headerAndData.length > objectIndex) {
          headerString = headerAndData[objectIndex].replace(/\\/g, '').replace(/\"\"/g, '"').trim().replace(/^"|"$/g, '');
          Logger.logInfo("HTTPConnection", "getObjectFromChunks", `Header string: ${JSON.parse(headerString)}`);
          resolve(headerString)
        }
        else {
          resolve('')
        }
      };
    })
  }

  private async updateJwtToken(jwtToken: string): Promise<void> {
    if (jwtToken) {
      jwtAuthToken = jwtToken;
      const unviredAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
      if (unviredAccount) {
        unviredAccount.setJwtToken(jwtAuthToken)
        UnviredAccountManager.getInstance().saveAccount(unviredAccount, AuthenticationService.instance.loginParameters.appName)
      }
      try {
        await UserSettingsManager.getInstance().setFieldValue(UserSettingsFields.jwtToken, jwtAuthToken);
      } catch (e) {
        Logger.logError("HTTPConnection", "updateJwtToken", "Error: " + JSON.stringify(e));
      }
      NotificationListenerHelper.postDataSenderNotification(jwtAuthToken, NotificationListnerType.jwtTokenUpdated);
    }
  }
}

export default new HttpConnection()