import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import * as FieldConstants from "../applicationMeta/fieldConstants";
import * as ServiceConstants from "../helper/serviceConstants";
import FrameworkHelper from "../helper/frameworkHelper";
import { UnviredAccountManager } from "../helper/unviredAccountManager";
import { isServerReachable } from "../helper/utils";
import { HttpConnection } from "../helper/httpConnection";
import { Status } from "../helper/status";
import AttachmentHelper from "./attachmentHelper";
import { kAttachmentItem, kError, NotificationListenerHelper, NotificationListnerType } from "../helper/notificationListnerHelper";

declare var Logger: any;

const fileName = "AttachmentService"

export default class AttachmentService {
    private static instance: AttachmentService;
    private isRunning: boolean = false;
    private shouldStop: boolean = false;

    private constructor() {}

    public static getInstance(): AttachmentService {
        if (!AttachmentService.instance) {
            AttachmentService.instance = new AttachmentService();
        }
        return AttachmentService.instance;
    }

    public start(): void {
        if (!this.isRunning) {
            this.isRunning = true;
            this.shouldStop = false;
            this.makeNetworkCallToDownloadAttachment();
        } else {
            Logger.logInfo(fileName, "start", "Attachment download service is already running.");
        }
    }

    public stop(): void {
        if (this.isRunning) {
            this.shouldStop = true;
            Logger.logInfo(fileName, "stop", "Stopping attachment download service.");
        } else {
            Logger.logInfo(fileName, "stop", "Attachment download service is not running.");
        }
    }
    private async makeNetworkCallToDownloadAttachment(): Promise<void> {
        let continueDownloading = false;
        const map: any = {};
        try {
            do {
                if (this.shouldStop) {
                    Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", "Stop requested. Exiting attachment download loop.");
                    break;
                }
                const databaseManager = DatabaseManager.getInstance();
                const attachmentQObject = await databaseManager.getFirstEntity(DatabaseType.FrameworkDb, "AttachmentQObject");
                continueDownloading = attachmentQObject !== null;
                if (attachmentQObject !== null) {
                    const attachmentQObjectData = attachmentQObject;
                    Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `-------DOWNLOADING ATTACHMENT - UID:${attachmentQObjectData.uid}------`)
                    Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `Processing ATTACHMENT UID : ${attachmentQObjectData.uid}.`);
        
                    const attachmentItems = await this.getAttachmentItems(attachmentQObjectData);
                    if (attachmentItems.length === 0) {
                        Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `Unable to get attachment item from database for UID: ${attachmentQObjectData.uid}. Structure Name: ${attachmentQObjectData.beAttachmentStructName}. So, Deleting the attachmentQObject.`);
                        try {
                            await databaseManager.delete(DatabaseType.FrameworkDb, "AttachmentQObject", `uid = '${attachmentQObjectData.uid}'`);
                        } catch (e) {
                            Logger.logError(fileName, "makeNetworkCallToDownloadAttachment", `Failed to remove attachment from attachmentQObject. UID: ${attachmentQObjectData.uid}---${JSON.stringify(e)}`);
                        }
                        continue;
                    }
        
                    const attachmentItem = attachmentItems[0];
                    let retryDownload = false;
                    let data: any = {};
        
                    try {
                        do {
                            retryDownload = false;
                            if (!navigator.onLine) {
                                continueDownloading = false;
                                Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `-------DOWNLOADING ATTACHMENT STOPPED BECAUSE OF INTERNET CONNECTION - UID:${attachmentQObjectData.uid}------`)
                                break;
                            }
                            
                            const lastLoggedInAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
                            if (!(await isServerReachable(lastLoggedInAccount.getServerURL()))) {
                                continueDownloading = false;
                                Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `-------DOWNLOADING ATTACHMENT STOPPED BECAUSE OF SERVER NOT REACHABLE - UID:${attachmentQObjectData.uid}------`)
                                break;
                            }
        
                            const result = await new HttpConnection().downloadAttachment(attachmentQObjectData.uid);
        
                            if (result.status === Status.ok) {
                                Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `-------ATTACHMENT DOWNLOADED SUCCESSFULLY - UID:${attachmentQObjectData.uid}------`)
                                let attachmentName = attachmentItem[ServiceConstants.AttachmentItemFieldUid];
                                if (attachmentItem[ServiceConstants.AttachmentItemFieldFileName] != null && attachmentItem[ServiceConstants.AttachmentItemFieldFileName] != "") {
                                    attachmentName = attachmentItem[ServiceConstants.AttachmentItemFieldFileName];
                                }
                                const arrayBuffer = await result.arrayBuffer();
                                try {
                                    const attachmentPath = await AttachmentHelper.addAttachment(attachmentName, arrayBuffer);
                                    attachmentItem[ServiceConstants.AttachmentItemFieldLocalPath] = attachmentPath;
                                    attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = ServiceConstants.AttachmentStatusDownloaded;
                                    NotificationListenerHelper.postDataSenderNotification(attachmentItem, NotificationListnerType.attachmentDownloadSuccess);
                                    Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `-------ATTACHMENT SAVED SUCCESSFULLY - UID:${attachmentQObjectData.uid}------`)
                                }
                                catch (error) {
                                    continueDownloading = false;
                                    const errorMessage = "Could not save the file: Error while trying to save the file. Error: " + error;
                                    Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `------- ERROR ON SAVING ATTACHMENT - UID:${attachmentQObjectData.uid}------`)
                                    Logger.logError(fileName, "makeNetworkCallToDownloadAttachment", errorMessage);
                                    const notifData = {
                                        [kAttachmentItem]: attachmentItem,
                                        [kError]: errorMessage
                                    }
                                    NotificationListenerHelper.postDataSenderNotification(notifData, NotificationListnerType.attachmentDownloadError);
                                }
                            } else if (result.status === Status.gone) {
                                Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `------- ERROR WHILE DOWNLOADING ATTACHMENT (410) - UID:${attachmentQObjectData.uid}------`)
                                const response = await result.json();
                                Logger.logError(fileName, "makeNetworkCallToDownloadAttachment", `Server status code 410. Response ${JSON.stringify(response)}`);
        
                                const errorMessage = "Error while downloading attachment.";
                                attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = ServiceConstants.AttachmentStatusErrorInDownload;
                                attachmentItem[ServiceConstants.AttachmentItemFieldMessage] = errorMessage;
                                continueDownloading = false;
                                const notifData = {
                                    [kAttachmentItem]: attachmentItem,
                                    [kError]: errorMessage
                                }
                                NotificationListenerHelper.postDataSenderNotification(notifData, NotificationListnerType.attachmentDownloadError);
                            } else if (result.status === Status.noContent) {
                                Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `------- NO CONTENT (204) ON DOWNLOADING ATTACHMENT - UID:${attachmentQObjectData.uid}------`)
                                Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `Server Status code 204. Retry downloading after 10 seconds.`);
        
                                await new Promise(resolve => setTimeout(resolve, 10000));
                                if (map[attachmentQObjectData.uid] == null) {
                                    map[attachmentQObjectData.uid] = 0;
                                }
        
                                retryDownload = map[attachmentQObjectData.uid] < 3;
                                map[attachmentQObjectData.uid]++;
        
                                if (!retryDownload) {
                                    Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `Retry download limit reached. Deleting the attachment Q object.`);
        
                                    const errorMessage = "Unable to download the attachment.";
                                    attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = ServiceConstants.AttachmentStatusErrorInDownload;
                                    attachmentItem[ServiceConstants.AttachmentItemFieldMessage] = errorMessage;
                                    const notifData = {
                                        [kAttachmentItem]: attachmentItem,
                                        [kError]: errorMessage
                                    }
                                    NotificationListenerHelper.postDataSenderNotification(notifData, NotificationListnerType.attachmentDownloadError);
                                }
                            } else {
                                Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `------- ERROR WHILE DOWNLOADING ATTACHMENT (${result.status}) - UID:${attachmentQObjectData.uid}------`)
                                Logger.logError(fileName, "makeNetworkCallToDownloadAttachment", `Server status code ${result.status}. Response ${JSON.stringify(result.body)}`);
        
                                const response = await result.json();
                                let errorMessage = "Unknown Error";
                                if (response[ServiceConstants.KeyError] != null) {
                                    errorMessage = response[ServiceConstants.KeyError];
                                }
                                if (response[ServiceConstants.KeyAttachmentResponse] != null &&
                                    (response[ServiceConstants.KeyAttachmentResponse])[ServiceConstants.KeyMessage_]) {
                                    errorMessage = (response[ServiceConstants.KeyAttachmentResponse])[ServiceConstants.KeyMessage_];
                                }

                                attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = ServiceConstants.AttachmentStatusErrorInDownload;
                                attachmentItem[ServiceConstants.AttachmentItemFieldMessage] = errorMessage;
                                const notifData = {
                                    [kAttachmentItem]: attachmentItem,
                                    [kError]: errorMessage
                                }
                                NotificationListenerHelper.postDataSenderNotification(notifData, NotificationListnerType.attachmentDownloadError);
                            }
                        } while (retryDownload);
                    } catch (e) {
                        Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `------- EXCEPTION WHILE DOWNLOADING ATTACHMENT - UID:${attachmentQObjectData.uid}------`)
                        Logger.logError(fileName, "makeNetworkCallToDownloadAttachment", `Error while downloading attachment. Error: ${JSON.stringify(e)}`);
                        attachmentItem[ServiceConstants.AttachmentItemFieldAttachmentStatus] = ServiceConstants.AttachmentStatusErrorInDownload;
                        attachmentItem[ServiceConstants.AttachmentItemFieldMessage] = `Error while downloading attachment. Error: ${JSON.stringify(e)}`;
                    }
        
                    try {
                        const result = await databaseManager.update(DatabaseType.AppDb, attachmentQObjectData.beAttachmentStructName, attachmentItem, `${ServiceConstants.AttachmentItemFieldUid} = '${attachmentItem[ServiceConstants.AttachmentItemFieldUid]}'`);
                        Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `Updated attachment into database. Attachment Structure Name: ${attachmentQObjectData.beAttachmentStructName}. UID: ${attachmentItem[ServiceConstants.AttachmentItemFieldUid]}. Result: ${JSON.stringify(result)}`);
                    } catch (e) {
                        Logger.logError(fileName, "makeNetworkCallToDownloadAttachment", `Error while updating attachment into database. Attachment Structure Name: ${attachmentQObjectData.beAttachmentStructName}. UID: ${attachmentItem[ServiceConstants.AttachmentItemFieldUid]}. Error: ${e}`);
                    }
        
                    try {
                        databaseManager.delete(DatabaseType.FrameworkDb, "AttachmentQObject", `uid = '${attachmentQObjectData.uid}'`);
                    } catch (e) {
                        Logger.logError(fileName, "makeNetworkCallToDownloadAttachment", `Error while removing attachment from attachmentQObject. UID: ${attachmentQObjectData.uid}---${e.toString()}`);
                    }
        
                    if (map[attachmentQObjectData.uid] != null) {
                        delete map[attachmentQObjectData.uid];
                    }
                    Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `------- CURRENT ATTACHMENT DOWNLOAD PROCESS IS COMPLETED - UID:${attachmentQObjectData.uid}------`)
                }
            } while (continueDownloading);
            await this.checkAndUpdateAttachmentDownloadStatus();
            Logger.logInfo(fileName, "makeNetworkCallToDownloadAttachment", `Stopping the attachment service.`); 
        }
        catch (e) {
            Logger.logError(fileName, "makeNetworkCallToDownloadAttachment", `Error while downloading attachment. Error: ${e}`);
        }
        finally {
            this.isRunning = false;
            this.shouldStop = false;  
        }
    }
    
    private async getAttachmentItems(attachmentQObjectData: any): Promise<any[]> {
        const databaseManager = DatabaseManager.getInstance();
        const whereClause = `${ServiceConstants.AttachmentItemFieldUid} = '${attachmentQObjectData.uid}'`;
        const attachmentItems = await databaseManager.select(DatabaseType.AppDb, `${attachmentQObjectData.beAttachmentStructName}`, whereClause);
        return attachmentItems;
    }
    
    private async checkAndUpdateAttachmentDownloadStatus(): Promise<void> {
        try {
            const databaseManager = DatabaseManager.getInstance()
            const attachmentsStructMetas = await databaseManager.select(DatabaseType.FrameworkDb, "StructureMeta", `structureName LIKE '%${ServiceConstants.AttachmentBE}'`);
            const allBusinessEntityMetas = await databaseManager.select(DatabaseType.FrameworkDb, "BusinessEntityMeta");
    
            const allAttachmentQItems = await databaseManager.select(DatabaseType.FrameworkDb, "AttachmentQObject");
            const allAttachmentBeLids = allAttachmentQItems.map((e:any) => e.uid);
            let attachmentUidsString = "";
            if (allAttachmentBeLids.length > 0) {
                attachmentUidsString = "'" + allAttachmentBeLids.join("','") + "'";
            }
    
            for (const attachmentsStructMeta of attachmentsStructMetas) {
                try {
                    const currentBeMeta =
                        allBusinessEntityMetas.find(
                            (element) => element.beName === attachmentsStructMeta.beName);
                    if (currentBeMeta!.save === "false" || currentBeMeta!.attachments !== "1") {
                        continue;
                    }
                    const allData = await databaseManager.select(DatabaseType.AppDb, attachmentsStructMeta.structureName, `${ServiceConstants.AttachmentItemFieldAttachmentStatus}='${ServiceConstants.AttachmentStatusQueuedForDownload}'` + (attachmentUidsString.length > 0 ? ` AND ${ServiceConstants.AttachmentItemFieldUid} NOT IN (${attachmentUidsString})`: ""))
                    for (const attachmentData of allData) {
                        try {
                            await databaseManager.update(DatabaseType.AppDb, attachmentsStructMeta.structureName, {
                                [ServiceConstants.AttachmentItemFieldAttachmentStatus]: ServiceConstants.AttachmentStatusErrorInDownload,
                                [ServiceConstants.AttachmentItemFieldMessage]: "The connection was interrupted. Please try again."
                            }, `${ServiceConstants.AttachmentItemFieldUid} = '${attachmentData[ServiceConstants.AttachmentItemFieldUid]}'`, true); // isFromApp is true because it just need to update the data and should not set the missing fields to null. 
                        } catch (e) {
                            Logger.logError(fileName, "checkAndUpdateAttachmentDownloadStatus", `Error while updating (${attachmentsStructMeta.structureName}) attachment download status into database. Error: ${e}`);
                        }
                    }
                } catch (e) {
                    Logger.logError(fileName, "checkAndUpdateAttachmentDownloadStatus", `Error while getting BE Meta for (${attachmentsStructMeta.structureName}). Error: ${e}`);
                }
            }
        } catch (e) {
            Logger.logError(fileName, "checkAndUpdateAttachmentDownloadStatus", `Error while updating attachment download Status to ERROR. Error: ${e}`);
        }
    }
}