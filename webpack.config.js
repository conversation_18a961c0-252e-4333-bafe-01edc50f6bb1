const path = require("path");
const CopyPlugin = require("copy-webpack-plugin");
const RemovePlugin = require("remove-files-webpack-plugin");
const fs = require("fs");
const TerserPlugin = require("terser-webpack-plugin");
const WebpackShellPluginNext = require("webpack-shell-plugin-next");

class ModifyPackageJsonPlugin {
  constructor(options) {
    this.options = options;
  }
  apply(compiler) {
    compiler.hooks.emit.tapAsync(
      "ModifyPackageJsonPlugin",
      (compilation, callback) => {
        const packageJsonPath = path.resolve(process.cwd(), "package.json");
        const packageJsonContent = fs.readFileSync(packageJsonPath, "utf8");
        const modifiedPackageJsonContent = this.modifyPackageJson(packageJsonContent);

        const outputPath = path.resolve(process.cwd(), this.options.outputPath);
        this.ensureDirectoryExistence(outputPath);
        fs.writeFileSync(outputPath, modifiedPackageJsonContent);
        callback();
      }
    );
  }

  modifyPackageJson(packageJsonContent) {
    const packageJson = JSON.parse(packageJsonContent);
    delete packageJson.dependencies;
    delete packageJson.devDependencies;
    delete packageJson.volta;
    packageJson.scripts = {};
    return JSON.stringify(packageJson, null, 2);
  }

  ensureDirectoryExistence(filePath) {
    const dirname = path.dirname(filePath);
    if (!fs.existsSync(dirname)) {
      fs.mkdirSync(dirname, { recursive: true });
    }
  }
}

module.exports = [
  {
    mode: "development",
    externals: {
      "cordova/exec": 'commonjs cordova/exec',
    },
    entry: "./www/kernel.js",
    output: {
        filename: 'kernel.js',
        path: path.resolve(__dirname, "build/cordova-plugin-unvired-universal-sdk/www"),
        libraryTarget: 'umd'
    },
    optimization: {
      minimize: false,
      minimizer: [new TerserPlugin()],
    },
    resolve: {
        extensions: ['.ts', '.tsx', '.js']
    },
    module: {
      rules: [
        {
          test: /\.(js|ts)x?$/,
          use: 'babel-loader',
          exclude: /node_modules/,
        },
      ],
    },
    resolve: {
      extensions: [ ".ts", ".js"],
      fallback: {
        "child_process": false,
        "os": false,
        "crypto": false,
        "stream": false,
        "buffer": false,
        "util": false,
        "path": false,
        "fs": false,
        "worker_threads": false
      }
    },
    devtool: "inline-source-map",
    plugins: [
      new WebpackShellPluginNext({
        onBuildEnd:{
          scripts: ['node ./scripts/automate.js'],
          blocking: false,
          parallel: true
        }
      }),
      new ModifyPackageJsonPlugin({
        outputPath: "./build/cordova-plugin-unvired-universal-sdk/package.json",
      }),
      new RemovePlugin({
        before: {
          include: ["./build"],
        },
      }),
      new CopyPlugin({
        patterns: [
          {
            from: '**/*',
            to: path.resolve(__dirname, 'build/cordova-plugin-unvired-universal-sdk'),
            context: path.resolve(__dirname, './'),
            globOptions: {
              ignore: ["**/dist/**", "**/*.json","**/node_modules/**","**/*config.js","**/scripts/**","**/*.ts","**/*build*.xml","**/*ivy*.xml","**/getSemanticVersion.js"],
            },
          }
        ],
      }),
    ],
  }
];