import { exec } from "child_process";
import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import ApplicationMetaParser from '../applicationMeta/applicationMetadataParser';
import * as FieldConstants from "../applicationMeta/fieldConstants";

declare var Logger: any;

export default class DbCreateTablesManager {
    fileName = "DbCreateTablesManager"
    parser = ApplicationMetaParser.instance;
    databaseManager: DatabaseManager | null = null;

    constructor() {}

    public async createTables(): Promise<boolean> { 
        try {
            Logger.logInfo(this.fileName, "createTables", "Creating all DB tables");
            this.databaseManager = DatabaseManager.getInstance();
            var result = await this.createFrameworkTables();
            if(result) {
                result = await this.createAppTables();
            }
            return result;
        } catch (e) {
            Logger.logError(this.fileName, "createTables", "Error while creating all DB tables. Error: " + e);
        }
        return false;
    }

    async createFrameworkTables(): Promise<boolean> {
        try {
            Logger.logInfo(this.fileName, "createFrameworkTables", "Creating all FW DB tables");
            const createTableQueries = [`CREATE TABLE IF NOT EXISTS ApplicationMeta (lid TEXT, timestamp INTEGER, objectStatus INTEGER, syncStatus INTEGER, appId TEXT, appName TEXT, description TEXT, version TEXT, installationDate TEXT, appClassName TEXT, PRIMARY KEY (lid), UNIQUE (appName))`,
            `CREATE TABLE IF NOT EXISTS BusinessEntityMeta (lid TEXT, timestamp INTEGER, objectStatus INTEGER, syncStatus INTEGER, appName TEXT, beName TEXT, description TEXT, addFunction TEXT, modifyFunction TEXT, deleteFunction TEXT, notification TEXT, attachments TEXT, conflictRules TEXT, save TEXT, PRIMARY KEY (lid), UNIQUE (appName, beName))`,
            `CREATE TABLE IF NOT EXISTS StructureMeta (lid TEXT, timestamp INTEGER, objectStatus INTEGER, syncStatus INTEGER, appName TEXT, beName TEXT, structureName TEXT, description TEXT, className TEXT, isHeader TEXT, PRIMARY KEY (lid), UNIQUE (appName, beName, structureName))`,
            `CREATE TABLE IF NOT EXISTS FieldMeta (lid TEXT, timestamp INTEGER, objectStatus INTEGER, syncStatus INTEGER, appName TEXT, beName TEXT, structureName TEXT, fieldName TEXT, description TEXT, length TEXT, mandatory TEXT, sqlType TEXT, isGid TEXT, PRIMARY KEY (lid), UNIQUE (appName, beName, structureName, fieldName))`,
            `CREATE TABLE IF NOT EXISTS Settings (lid TEXT, timestamp INTEGER, objectStatus INTEGER, syncStatus INTEGER, fieldName TEXT, fieldValue TEXT, PRIMARY KEY (lid), UNIQUE (fieldName))`,
            `CREATE TABLE IF NOT EXISTS FrameworkSettings (lid TEXT, timestamp INTEGER, objectStatus INTEGER, syncStatus INTEGER, fieldName TEXT, fieldValue TEXT, PRIMARY KEY (lid), UNIQUE (fieldName))`,
            `CREATE TABLE IF NOT EXISTS MobileUserSettings (lid TEXT, timestamp INTEGER, objectStatus INTEGER, syncStatus INTEGER, keyName TEXT, description TEXT, defaultField TEXT, current TEXT, mandatory TEXT, secure TEXT, PRIMARY KEY (lid), UNIQUE (lid))`,
            `CREATE TABLE IF NOT EXISTS InfoMessage (lid TEXT DEFAULT (UUID()), timestamp INTEGER DEFAULT (strftime('%s', 'now') * 1000), objectStatus INTEGER DEFAULT (0), syncStatus INTEGER DEFAULT (0), type TEXT, subtype TEXT, category TEXT, message TEXT, bename TEXT, belid TEXT, messagedetails BLOB, PRIMARY KEY (lid), UNIQUE (lid))`,
            `CREATE TABLE IF NOT EXISTS ConflictBE (lid TEXT DEFAULT (UUID()), timestamp INTEGER DEFAULT (strftime('%s', 'now') * 1000), objectStatus INTEGER DEFAULT (0), syncStatus INTEGER DEFAULT (0), beName TEXT, beHeaderLid TEXT, data TEXT, PRIMARY KEY (lid), UNIQUE (lid))`,
            `CREATE TABLE IF NOT EXISTS InObject (lid TEXT DEFAULT (UUID()), timestamp INTEGER DEFAULT (strftime('%s', 'now') * 1000), objectStatus INTEGER DEFAULT (0), syncStatus INTEGER DEFAULT (0), conversationId TEXT, subtype INTEGER, type INTEGER, appId TEXT, serverId TEXT, appName TEXT, requestType TEXT, jsonData TEXT, beLid TEXT, PRIMARY KEY (lid, conversationId), UNIQUE (conversationId))`,
            `CREATE TABLE IF NOT EXISTS OutObject (lid TEXT DEFAULT (UUID()), timestamp INTEGER DEFAULT (strftime('%s', 'now') * 1000), objectStatus INTEGER DEFAULT (0), syncStatus INTEGER DEFAULT (0), functionName TEXT, beName TEXT, beHeaderLid TEXT, requestType TEXT, syncType TEXT, conversationId TEXT, messageJson TEXT, companyNameSpace TEXT, sendStatus TEXT, fieldOutObjectStatus TEXT, isAdminServices BOOLEAN, PRIMARY KEY (lid), UNIQUE (lid))`,
            `CREATE TABLE IF NOT EXISTS SentItems (lid TEXT DEFAULT (UUID()), timestamp INTEGER DEFAULT (strftime('%s', 'now') * 1000), objectStatus INTEGER DEFAULT (0), syncStatus INTEGER DEFAULT (0), beName TEXT, beHeaderLid TEXT, conversationId TEXT, entryDate TEXT, attachmentFlag TEXT, PRIMARY KEY (lid), UNIQUE (conversationId))`,
            `CREATE TABLE IF NOT EXISTS AttachmentQObject (lid TEXT DEFAULT (UUID()), timestamp INTEGER DEFAULT (strftime('%s', 'now') * 1000), objectStatus INTEGER DEFAULT (0), syncStatus INTEGER DEFAULT (0), uid TEXT, beName TEXT, beHeaderName TEXT, beAttachmentStructName TEXT, priority INTEGER, PRIMARY KEY (uid), UNIQUE (uid))`,
            `CREATE TABLE IF NOT EXISTS SystemCredentials (lid TEXT DEFAULT (UUID()), timestamp INTEGER DEFAULT (strftime('%s', 'now') * 1000), objectStatus INTEGER DEFAULT (0), syncStatus INTEGER DEFAULT (0), name TEXT, portName TEXT, portType TEXT, portDesc TEXT, systemDesc TEXT, userId TEXT, password TEXT, PRIMARY KEY (lid), UNIQUE (portName))`];
            for (const query of createTableQueries) {
                try {
                    var createTableResult = await this.databaseManager.executeStatement(DatabaseType.FrameworkDb, query);
                    Logger.logInfo(this.fileName, "createFrameworkTables", "Result: " + createTableResult);
                }
                catch (e) {
                    Logger.logError(this.fileName, "createFrameworkTables", "Error while creating FW DB tables. Error: " + e);
                }
            }
            var result = await this.insertParsedDataIntoFrameworkTables();
            return result;
        } catch (e) {
            Logger.logError(this.fileName, "insertParsedDataIntoFrameworkTables", "Error while creating all FW DB tables. Error: " + e);
        }
        return false;
    }

    async insertParsedDataIntoFrameworkTables(): Promise<boolean> {
        try {
            Logger.logInfo(this.fileName, "insertParsedDataIntoFrameworkTables", "Adding application meta into FW DB");
            const applicationMeta = this.parser.getApplicationMeta();
            const insertApplicationMetaQuery = `INSERT INTO ApplicationMeta (lid, timestamp, objectStatus, syncStatus, appId, appName, description, version, installationDate, appClassName) VALUES ('${applicationMeta.lid}', ${applicationMeta.timestamp}, ${applicationMeta.objectStatus}, ${applicationMeta.syncStatus}, '${applicationMeta.appId}', '${applicationMeta.appName}', '${applicationMeta.description}', '${applicationMeta.version}', '${applicationMeta.installationDate}', '${applicationMeta.appClassName}')`;
            await this.databaseManager.executeStatement(DatabaseType.FrameworkDb, insertApplicationMetaQuery);
        } catch (e) {
            Logger.logError(this.fileName, "insertParsedDataIntoFrameworkTables", "Error while adding Application Meta. Error: " + e);
            return false;
        }

        try {
            Logger.logInfo(this.fileName, "insertParsedDataIntoFrameworkTables", "Adding businessEntityMeta into FW DB");
            const businessEntityMetas = this.parser.getBusinessEntityMetas();
            const insertBusinessEntityMetaQuery = `INSERT INTO BusinessEntityMeta (lid, timestamp, objectStatus, syncStatus, appName, beName, description, addFunction, modifyFunction, deleteFunction, notification, attachments, conflictRules, save) VALUES ${businessEntityMetas.map(meta => `('${meta.lid}', ${meta.timestamp}, ${meta.objectStatus}, ${meta.syncStatus}, '${meta.appName}', '${meta.beName}', '${meta.description}', '${meta.addFunction}', '${meta.modifyFunction}', '${meta.deleteFunction}', '${meta.notification}', '${meta.attachments}', '${meta.conflictRules}', '${meta.save}')`).join(', ')}`;
            await this.databaseManager.executeStatement(DatabaseType.FrameworkDb, insertBusinessEntityMetaQuery);
        } catch (e) {
            Logger.logError(this.fileName, "insertParsedDataIntoFrameworkTables", "Error while adding businessEntityMeta. Error: " + e);
            return false;
        }

        try {
            Logger.logInfo(this.fileName, "insertParsedDataIntoFrameworkTables", "Adding structureMeta into FW DB");
            const structureMetas = this.parser.getStructureMetas();
            const insertStructureMetaQuery = `INSERT INTO StructureMeta (lid, timestamp, objectStatus, syncStatus, appName, beName, structureName, description, className, isHeader) VALUES ${structureMetas.map(meta => `('${meta.lid}', ${meta.timestamp}, ${meta.objectStatus}, ${meta.syncStatus}, '${meta.appName}', '${meta.beName}', '${meta.structureName}', '${meta.description}', '${meta.className}', ${meta.isHeader})`).join(', ')}`;
            await this.databaseManager.executeStatement(DatabaseType.FrameworkDb, insertStructureMetaQuery);
        } catch (e) {
            Logger.logError(this.fileName, "insertParsedDataIntoFrameworkTables", "Error while adding structureMeta. Error: " + e);
            return false;
        }

        try {
            Logger.logInfo(this.fileName, "insertParsedDataIntoFrameworkTables", "Adding fieldMeta into FW DB");
            const fieldMetas = this.parser.getFieldMetas();
            const insertFieldMetaQuery = `INSERT INTO FieldMeta (lid, timestamp, objectStatus, syncStatus, appName, beName, structureName, fieldName, description, length, mandatory, sqlType, isGid) VALUES ${fieldMetas.map(meta => `('${meta.lid}', ${meta.timestamp}, ${meta.objectStatus}, ${meta.syncStatus}, '${meta.appName}', '${meta.beName}', '${meta.structureName}', '${meta.fieldName}', '${meta.description.replace(/'/g, "''")}', '${meta.length}', '${meta.mandatory}', '${meta.sqlType}', ${meta.isGid})`).join(', ')}`;
            await this.databaseManager.executeStatement(DatabaseType.FrameworkDb, insertFieldMetaQuery);
        } catch (e) {
            Logger.logError(this.fileName, "insertParsedDataIntoFrameworkTables", "Error while adding fieldMeta. Error: " + e);
            return false;
        }
        return true;
    }

    async createAppTables(): Promise<boolean> {
        const structureMetas = this.parser.getStructureMetas();

        if (structureMetas.length === 0) {
            Logger.logError("ApplicationManager", "createAppTables", "Invalid metadata.");
            throw "Invalid metadata.";
        }

        const beMetaArray = this.parser.getBusinessEntityMetas();
        const fieldMetaArray = this.parser.getFieldMetas();

        const map: { [key: string]: any } = {
            "structureMetas": structureMetas,
            "beMetaArray": beMetaArray,
            "fieldMetaArray": fieldMetaArray
        };

        // const queries: string[] = await compute(appTableGenerator, map);
        const queries: string[] = this.appTableGenerator(map);

        for (const query of queries) {
            if (query.length === 0) {
                throw ("Invalid input json data");
            }
            try {
                Logger.logInfo("ApplicationManager", "createAppTables", "Executing query : " + query);
                await this.databaseManager.executeStatement(DatabaseType.AppDb, query);
            } catch (e) {
                Logger.logError("ApplicationManager", "createAppTables", "Error while creating tables. Error: " + e);
                throw (e);
            }
        }

        for (const indexMeta of this.parser.getIndexMetas()) {
            const queryString: string = this.prepareCreateIndexQuery(indexMeta.indexName, indexMeta.structureName, indexMeta.fieldName);
            try {
                Logger.logInfo("ApplicationManager", "createAppTables", "Executing query : " + queryString);
                await this.databaseManager.executeStatement(DatabaseType.AppDb, queryString);
            } catch (e) {
                Logger.logError("ApplicationManager", "createAppTables", "Error while creating Index. Error: " + e);
                throw e;
            }
        }

        return true;
    }

    prepareCreateTableQuery(
        tableName: string,
        tableColumnNames: string[],
        tableColumnTypes: any[],
        primaryKeys: string[],
        uniqueKeys: string[],
        mandatoryFields: boolean[],
        { foreignKeys = [], foreignKeyTableName = "", foreignKeyTableKeys = [] }: {
            foreignKeys?: string[],
            foreignKeyTableName?: string,
            foreignKeyTableKeys?: string[]
        } = {}
    ): string {
        let query: string = "";
        const primaryKey: string = primaryKeys.join(", ");
        const uniqueKey: string = uniqueKeys.join(", ");
        let foreignKey: string = "";
        let foreignKeyTableKey: string = "";

        if (foreignKeyTableName.length > 0) {
            foreignKey = foreignKeys.join(", ");
            foreignKeyTableKey = foreignKeyTableKeys.join(", ");
        }

        const noOfColumns: number = tableColumnNames.length;
        query += `CREATE TABLE IF NOT EXISTS ${tableName} (`;

        for (let i = 0; i < noOfColumns; i++) {
            if (i === 0) {
                query += tableColumnNames[i];
            } else {
                query += `, ${tableColumnNames[i]}`;
            }

            if (tableColumnTypes.length > 0) {
                query += ` ${tableColumnTypes[i]}`;
            }

            if (mandatoryFields.length > 0) {
                if (mandatoryFields[i]) {
                    query += " NOT NULL";
                }
            }
        }

        if (primaryKey.length > 0) {
            query += `, PRIMARY KEY(${primaryKey})`;
        }

        if (uniqueKey.length > 0) {
            query += `, UNIQUE(${uniqueKey})`;
        }

        if (foreignKeyTableName.length > 0) {
            query += `, FOREIGN KEY (${foreignKey}) REFERENCES ${foreignKeyTableName}(${foreignKeyTableKey}) ON DELETE CASCADE`;
        }
        query += ")";
        return query;
    }

    prepareCreateIndexQuery(indexName: string, structureName: string, fieldNames: string[]): string {
        let query: string = `CREATE INDEX ${indexName} ON ${structureName} (`;
        query += fieldNames.join(", ");
        query += ")";
        return query;
    }

    appTableGenerator(message: { [key: string]: any }) {
        const queries: string[] = [];
        const structureMetas = message['structureMetas'];
        const beMetaArray = message['beMetaArray'];
        const fieldMetaArray = message['fieldMetaArray'];

        const noOfStructureMetas: number = structureMetas.length;

        let structureMeta;
        const foreignKeysForItemStructure: string[] = [FieldConstants.FieldFid];
        const parentsForForeignKeysInHeaderStructure: string[] = [FieldConstants.FieldLid];

        for (let i = 0; i < noOfStructureMetas; i++) {
            structureMeta = structureMetas[i];

            const beName: string = structureMeta.beName;
            if (beName.length === 0) {
                continue;
            }

            const beMeta = beMetaArray.find((element: any) => element.beName === beName);
            if (beMeta == null || beMeta.save !== "true") {
                continue;
            }

            const fieldMetas = fieldMetaArray.filter((element: any) =>
                element.appName === structureMeta.appName &&
                element.beName === structureMeta.beName &&
                element.structureName === structureMeta.structureName
            );

            if (fieldMetas.length === 0) {
                throw "Invalid metadata.";
            }

            const columnNames: string[] = [];
            const columnTypes: any[] = [];
            const mandatoryFields: boolean[] = [];

            const gidsVector: string[] = [];

            const primaryKeys: string[] = [FieldConstants.FieldLid];
            const isStructureHeader: boolean = structureMeta.isHeader === "1";

            columnNames.push(FieldConstants.FieldLid);
            columnTypes.push(FieldConstants.FieldTypeLid);
            mandatoryFields.push(true);

            columnNames.push(FieldConstants.FieldTimestamp);
            columnTypes.push(FieldConstants.FieldTypeTimestamp);
            mandatoryFields.push(true);

            columnNames.push(FieldConstants.FieldSyncStatus);
            columnTypes.push(FieldConstants.FieldTypeSyncStatus);
            mandatoryFields.push(true);

            columnNames.push(FieldConstants.FieldObjectStatus);
            columnTypes.push(FieldConstants.FieldTypeObjectStatus);
            mandatoryFields.push(true);

            if (isStructureHeader) {
                columnNames.push(FieldConstants.FieldConflict);
                columnTypes.push(FieldConstants.FieldTypeConflict);
                mandatoryFields.push(false);

                columnNames.push(FieldConstants.FieldInfoMsgCat);
                columnTypes.push(FieldConstants.FieldTypeInfoMsgCat);
                mandatoryFields.push(false);
            }

            if (!isStructureHeader) {
                columnNames.push(FieldConstants.FieldFid);
                columnTypes.push(FieldConstants.FieldTypeFid);
                mandatoryFields.push(true);
            }

            let fieldMeta: any;

            for (let j = 0; j < fieldMetas.length; j++) {
                fieldMeta = fieldMetas[j];

                const _columnName: string = fieldMeta.fieldName;
                const _columnType: any = fieldMeta.sqlType;

                columnNames.push(_columnName);
                columnTypes.push(_columnType);

                if (fieldMeta.isGid === "1") {
                    mandatoryFields.push(true);
                    gidsVector.push(fieldMeta.fieldName);
                } else {
                    mandatoryFields.push(false);
                }
            }

            const noOfGids: number = gidsVector.length;
            const uniqueKeys: string[] = gidsVector;

            if (isStructureHeader) {
                const queryString: string = this.prepareCreateTableQuery(
                    structureMeta.structureName,
                    columnNames,
                    columnTypes,
                    primaryKeys,
                    uniqueKeys,
                    mandatoryFields
                );
                queries.push(queryString);
            } else {
                let headerName: string = structureMeta.beName + "_HEADER";
                try {
                    const headerStructureMeta = structureMetas.find(
                        (element) =>
                            element.beName === structureMeta.beName &&
                            element.isHeader === "1"
                    );
                    if (headerStructureMeta) {
                        headerName = headerStructureMeta.structureName;
                    }
                } catch (e) {}
                const queryString: string = this.prepareCreateTableQuery(
                    structureMeta.structureName,
                    columnNames,
                    columnTypes,
                    primaryKeys,
                    uniqueKeys,
                    mandatoryFields,
                    { foreignKeys: foreignKeysForItemStructure, foreignKeyTableName: headerName, foreignKeyTableKeys: parentsForForeignKeysInHeaderStructure }
                );
                queries.push(queryString);
            }
        }
        return queries;
    }
}