export const FrameworkVersionNumber = "1.1.1";
export const FrameworkBuildNumber = "@BUILD_NUMBER@";
export const FrameworkRevisionNumber =
  "f4444dd36a88188c063db5fe24a4eaa8b7d22106";
export const FrameworkRevisionUrl = "";

export const ApplicationRevisionNumber = "99.99";

// Header Constants
export const HeaderConstantConversationId = "X-UNVIRED-CONVERSATION-ID";
export const HeaderConstantRequestType = "REQUEST_TYPE";
export const HeaderConstantNumberOfPendingMessages =
  "X-UNVIRED-NUMBER-OF-PENDING-MESSAGES";
export const HeaderConstantPullMode = "x-pull-mode";
export const HeaderConstantMessageType = "x-unvired-message-type";

// Send Logs/Data api params
export const ParamCompanyNamespace = "COMPANY_NAMESPACE";
export const ParamCompanyAlias = "COMPANY_ALIAS";
export const ParamAction = "ACTION";
export const ParamJwtToken = "JWT_TOKEN";
export const ParamOneTimeLoginToken = "X-ONE-TIME-LOGIN-TOKEN";
export const ParamApplication = "APPLICATION";
export const ParamAttachmentGuid = "X_ATTACHMENT_GUID";
export const ParamMimeType = "MIME_TYPE";
export const ParamServerId = "SERVER_ID";
export const ParamServerUserId = "MUSID";
export const ParamFeUserId = "FEUSERID";
export const ParamPassword = "PWD";
export const ParamOneTimeToken = "onetimetoken";
export const ParamMessageTime = "messagetime";
export const ParamLoginType = "LOGIN_TYPE";
export const ParamDeviceType = "DEVICE_TYPE";
export const ParamAppVersion = "cltAppVer";
export const ParamFrameworkVersion = "cltFwkVer";
export const ParamDeviceOsVersion = "devOS";
export const ParamDeviceModel = "devModel";
export const ParamDeviceState = "X-DEVICE-STATE";

// Send Logs/Data api ACTION param values
export const ActionUploadLogs = "UPLOAD_LOG";
export const ActionUploadData = "UPLOAD_DATA";

// endpoints
export const ServiceApiVersion = "API/v3/";
export const ServiceSession = "session";
export const ServiceApplications = "applications";
export const ServiceActivate = "activate";
export const ServiceLocal = "?local";
export const ServiceExecute = "execute";
export const ServiceMessage = "message";
export const ServiceAttachments = "attachments";
export const ServiceAttachment = "attachment";
export const ServiceAdminServices = "adminservices";
export const ServicePasswordLess = "passwordless";
export const ServiceStatus = "status";

export const AdminServicePing = "ping";
export const AdminServiceTestNotif = "testnotify";
export const AdminServiceInitialDownload = "initialdownload";
export const AdminServiceAuthBackend = "authbackend";

// route
export const ServiceFrontendUsers = "frontendusers";
export const ServiceConversation = "conversation";
export const ServiceMessages = "messages";

// Query Params
export const QueryParamFrontendUser = "frontendUser";
export const QueryParamInputMessage = "inputMessage";
export const QueryParamMessageFormat = "messageFormat";
export const QueryParamQueuedExecute = "queuedExecute";
export const QueryParamExternalReference = "externalReference";
export const QueryParamRequestType = "requestType";
export const QueryParamFile = "file";
export const QueryParamSensitive = "sensitive";
export const QueryParamCredentials = "credentials";

// Message Types
export const MessageTypeStandard = "standard";
export const MessageTypeCustom = "custom";

export const LoginTypeUnviredId = "UNVIRED-USER";
export const LoginTypeSAP = "SAP";
export const LoginTypeEmail = "EMAIL";
export const LoginTypeADS = "ACTIVE-DIRECTORY";
// export const LoginTypeLDAP = "LDAP";
export const LoginTypeCustom = "CUSTOM";
// export const LoginTypeSAML2 = "SAML2";

// Session Api response keys
export const KeyUnviredId = "unviredId";
export const KeyUnviredMd5Pwd = "md5Pwd";
export const KeySessionId = "sessionId";
export const KeyUsers = "users";
export const KeyFrontendType = "frontendType";
export const KeyApplications = "applications";
export const KeyName = "name";
export const KeyError = "error";
export const KeyToken = "token";
export const KeySettings = "settings";
export const KeyLocation = "location";
export const KeyAppSettings = "appsettings";
export const KeyLocationTracking = "tracking";
export const KeyLocationInterval = "interval";
export const KeyLocationUploadInterval = "uploadInterval";
export const KeyLocationDays = "days";
export const KeyLocationStart = "start";
export const KeyLocationEnd = "end";
export const KeySystems = "systems";
export const KeySystemsName = "name";
export const KeySystemsPortName = "portName";
export const KeySystemsPortType = "portType";
export const KeySystemsPortDesc = "portDescr";
export const KeySystemsDesc = "systemDescr";

// PA response keys
export const KeyMeta = "Meta";
export const KeyActionAttribute = "a";
export const KeyMetadataDelete = "d";
export const KeyBeName = "BEName";
export const KeyInfoMessage = "InfoMessage";

// Info Message Keys
export const KeyInfoMessageType = "type";
export const KeyInfoMessageSubtype = "subtype";
export const KeyInfoMessageCategory = "category";
export const KeyInfoMessageMessage = "message";
export const KeyInfoMessageBeName = "bename";
export const KeyInfoMessageBeLid = "belid";
export const KeyInfoMessageMessageDetails = "messagedetails";

// PA response header key
export const KeyJwtToken = "jwttoken";
export const PullModeDelete = "DELETE";

// Attachment response keys
export const KeyAttachmentResponse = "AttachmentResponse";
export const KeyMessage_ = "message";

// Action Type
export const ActionTypeA = "A";
export const ActionTypeM = "M";
export const ActionTypeD = "D";

// Attachment
export const AttachmentBE = "_ATTACHMENT";

// Attachment Status
export const AttachmentStatusDefault = "DEFAULT";
export const AttachmentStatusQueuedForDownload = "QUEUED_FOR_DOWNLOAD";
export const AttachmentStatusDownloaded = "DOWNLOADED";
export const AttachmentStatusErrorInDownload = "ERROR_IN_DOWNLOAD";
export const AttachmentStatusSavedForUpload = "SAVED_FOR_UPLOAD";
export const AttachmentStatusUploaded = "UPLOADED";
export const AttachmentStatusErrorInUpload = "ERROR_IN_UPLOAD";
export const AttachmentStatusMarkedForDelete = "MARKED_FOR_DELETE";
export const FwAttachmentForceDownloadPriority = 1;
export const FwAttachmentAutoDownloadPriority = 2;

// Attachment Item Fields
export const AttachmentItemFieldUid = "UID";
export const AttachmentItemFieldMimeType = "MIME_TYPE";
export const AttachmentItemFieldFileName = "FILE_NAME";
export const AttachmentItemFieldDescription = "DESCRIPTION";
export const AttachmentItemFieldUrl = "URL";
export const AttachmentItemFieldExternalUrl = "EXTERNAL_URL";
export const AttachmentItemFieldLocalPath = "LOCAL_PATH";
export const AttachmentItemFieldAutoDownload = "AUTO_DOWNLOAD";
export const AttachmentItemFieldAttachmentStatus = "ATTACHMENT_STATUS";
export const AttachmentItemFieldErrorMessage = "ERROR_MESSAGE";
export const AttachmentItemFieldMessage = "MESSAGE";

export const Type = "type";
export const Subtype = "subtype";
export const ServerId = "serverId";
export const ApplicationId = "applicationId";
export const AppName = "appName";

/**
 * Messages for the application.
 */
export const MESSAGE_TYPE_APPLICATION = 8000;
/**
 * Messages for the system / framework.
 */
export const MESSAGE_TYPE_SYSTEM = 9000;
/**
 * Message to clear all the data in the application.
 */
export const MESSAGE_TYPE_WIPE = 5000;
/**
 * Messages for authentication.
 */
export const MESSAGE_TYPE_AUTHENTICATION = 4000;

export const MESSAGE_SUBTYPE_SYSTEM_PING = 100;
export const MESSAGE_SUBTYPE_SYSTEM_LOG = 200;
export const MESSAGE_SUBTYPE_SYSTEM_LOG_RESET = 210;
export const MESSAGE_SUBTYPE_SYSTEM_LOG_SET_ERROR = 220;
export const MESSAGE_SUBTYPE_SYSTEM_LOG_SET_DEBUG = 230;
export const MESSAGE_SUBTYPE_SYSTEM_DATA_DUMP = 300;
export const MESSAGE_SUBTYPE_SYSTEM_INITIAL_DATA = 400;
export const MESSAGE_SUBTYPE_SYSTEM_APPLICATION_ASSIGN = 410;
export const MESSAGE_SUBTYPE_SYSTEM_APPLICATION_UNASSIGN = 430;
export const MESSAGE_SUBTYPE_SYSTEM_FRAMEWORK_SETTINGS = 500;
export const MESSAGE_SUBTYPE_SYSTEM_RESET_DATA = 700;
export const MESSAGE_SUBTYPE_SYSTEM_ACTIVATION = 800;
export const MESSAGE_SUBTYPE_SYSTEM_MULTIPLE_FRONTEND_USERS = 810;
export const MESSAGE_SUBTYPE_SYSTEM_ERROR = 900;
export const MESSAGE_SUBTYPE_SYSTEM_INFO = 1000;
export const MESSAGE_SUBTYPE_SYSTEM_INITIATE_PULL = 440;
export const MESSAGE_SUBTYPE_SYSTEM_PULL_COMPLETE = 450;
export const MESSAGE_SUBTYPE_SYSTEM_SEND_PUSH_NOTIFICATION_ID = 610;

export const MESSAGE_SUBTYPE_AUTHENTICATION = 100;
export const MESSAGE_SUBTYPE_AUTHENTICATION_AND_ACTIVATE = 200;
export const MESSAGE_SUBTYPE_SAP_AUTHENTICATE = 300;
export const MESSAGE_SUBTYPE_CHANGE_PASSWORD = 400;
export const MESSAGE_SUBTYPE_FORGOT_PASSWORD = 500;
export const MESSAGE_SUBTYPE_REGISTER_USER = 600;
export const MESSAGE_SUBTYPE_GET_LOGIN_TOKEN = 700;

export const MESSAGE_SUBTYPE_APPLICATION_USER_SETTNGS_UPDATE = 625;
export const MESSAGE_SUBTYPE_LOCATION_INFORMATION = 1100;
export const MESSAGE_SUBTYPE_CHECK_FOR_APP_UPGRADE = 1300;
export const MESSAGE_SUBTYPE_TEST_PUSH = 110;

export const logError = "ERROR";
export const logDebug = "DEBUG";
export const logImportant = "IMPORTANT";

//Network connections
export const online = "Online";
export const offline = "Offline";

export const DefaultHttpTimeout = 300; // in seconds

export enum RequestType {
  RQST = "RQST",
  PULL = "PULL",
  PUSH = "PUSH",
  QUERY = "QUERY",
  REQ = "REQ",
  PULLD = "PULLD",
}

export enum SyncType { ASYNC = "ASYNC", SYNC = "SYNC" }

export enum OutObjectStatus {
  none,
  lockedForModify,
  lockedForSending,
  errorOnProcessing
}