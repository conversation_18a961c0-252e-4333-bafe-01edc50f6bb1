
// Firebase Push Notification Service for iOS and Android
// Requires cordova-plugin-firebasex
import { convertXML } from 'simple-xml-to-json';
import { NotificationConstants } from './helper/notificationConstants';
import { UnviredAccountManager } from './helper/unviredAccountManager';
import SyncEngine from './syncEngine';

declare var Logger: any;

// Placeholder Notification class for compatibility. Replace with your actual implementation if available.

class Notification {
    notificationType: string = "";
    alert: string = "";
    title: string = "";
    actionButton: string = "";
    silent: boolean = false;
    badge: number = 0;
    category: string = "";
    sound: string = "default";
    attachmentUID: string = "";
    notificationContext: string = "";
    activationId: string = "";

    constructor(init?: Partial<Notification>) {
        Object.assign(this, init);
    }
}

export class FirebasePushService {
    static registerForPushNotifications(onRegister: (token: string) => void, onError?: (error: any) => void) {
        if (window && window['FirebasePlugin']) {
            // Request permission for push notifications
            window['FirebasePlugin'].hasPermission((hasPermission: boolean) => {
                if (!hasPermission) {
                    window['FirebasePlugin'].grantPermission((granted: boolean) => {
                        if (!granted) {
                            if (onError) onError('Push notification permission not granted');
                            return;
                        }
                        // Permission granted, proceed to register
                        FirebasePushService.registerTokenHandlers(onRegister, onError);
                    }, (error: any) => {
                        if (onError) onError('Error requesting push notification permission: ' + error);
                    });
                } else {
                    // Already has permission, proceed to register
                    FirebasePushService.registerTokenHandlers(onRegister, onError);
                }
            }, (error: any) => {
                if (onError) onError('Error checking push notification permission: ' + error);
            });
        } else {
            if (onError) onError('FirebasePlugin not available');
        }
    }

    private static registerTokenHandlers(onRegister: (token: string) => void, onError?: (error: any) => void) {
        window['FirebasePlugin'].onTokenRefresh((token: string) => {
            onRegister(token);
        }, (error: any) => {
            if (onError) onError(error);
        });
        window['FirebasePlugin'].getToken((token: string) => {
            onRegister(token);
        }, (error: any) => {
            if (onError) onError(error);
        });
    }

    static onNotificationReceived(onMessage: (data: any) => void, onError?: (error: any) => void) {
        if (window && window['FirebasePlugin']) {
            window['FirebasePlugin'].onMessageReceived((data: any) => {
                Logger.logDebug("FirebasePushService", "onNotificationReceived", "PUSH DATA: " + JSON.stringify(data));
                // Parse and handle the push notification payload
                onMessage(FirebasePushService.parseNotification(data));
            }, (error: any) => {
                Logger.logError("FirebasePushService", "onNotificationReceived", "PUSH ERROR: " + JSON.stringify(error));
                if (onError) onError(error);
            });
        } else {
            if (onError) onError('FirebasePlugin not available');
        }
    }

    static parseNotification(data: any): any {
        let parsedData = data.data || {};
        // If data.data is XML, convert it to JSON using simple-xml-to-json
        if (parsedData.length > 0 && typeof parsedData === 'string' && parsedData.trim().startsWith('<') && parsedData.trim().endsWith('>')) {
            try {
                parsedData = convertXML(parsedData);
            } catch (e) {
                // If conversion fails, keep as string
                return new Notification()
            }
        }
        else {
            return new Notification()
        }

        // TypeScript equivalent of Dart notification parsing logic
        let jsonData: any = {};
        const notificationDataJson = parsedData;
        if (notificationDataJson && typeof notificationDataJson === 'object') {
            if (notificationDataJson.hasOwnProperty(NotificationConstants.UNVIRED_PUSH_NOTIFICATION)) {
                jsonData = notificationDataJson[NotificationConstants.UNVIRED_PUSH_NOTIFICATION];
            } else if (notificationDataJson.hasOwnProperty(NotificationConstants.INDIENCE_PUSH_NOTIFICATION)) {
                jsonData = notificationDataJson[NotificationConstants.INDIENCE_PUSH_NOTIFICATION];
            } else if (notificationDataJson.hasOwnProperty(NotificationConstants.INDIENCE_PUSH_NOTIFICATION_TEMP)) {
                jsonData = notificationDataJson[NotificationConstants.INDIENCE_PUSH_NOTIFICATION_TEMP];
            }
        }

        const notification = new Notification();
        if (jsonData && Object.keys(jsonData).length > 0) {
            notification.notificationType = jsonData[NotificationConstants.UNDERSCORE_TYPE] || "";
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_MESSAGE)) {
                notification.alert = jsonData[NotificationConstants.PUSH_NOTIFICATION_MESSAGE] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_TITLE)) {
                notification.title = jsonData[NotificationConstants.PUSH_NOTIFICATION_TITLE] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_ACTION_BUTTON)) {
                notification.actionButton = jsonData[NotificationConstants.PUSH_NOTIFICATION_ACTION_BUTTON] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_SILENT)) {
                notification.silent = jsonData[NotificationConstants.PUSH_NOTIFICATION_SILENT].toString() === 'true';
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_COUNT)) {
                notification.badge = parseInt(jsonData[NotificationConstants.PUSH_NOTIFICATION_COUNT].toString(), 10);
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_CATEGORY)) {
                notification.category = jsonData[NotificationConstants.PUSH_NOTIFICATION_CATEGORY] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_SOUND)) {
                notification.sound = jsonData[NotificationConstants.PUSH_NOTIFICATION_SOUND] || "default";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_ATTACHMENT_ID)) {
                notification.attachmentUID = jsonData[NotificationConstants.PUSH_NOTIFICATION_ATTACHMENT_ID] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_CONTEXT)) {
                notification.notificationContext = jsonData[NotificationConstants.PUSH_NOTIFICATION_CONTEXT] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_ACTIVATION_ID)) {
                notification.activationId = jsonData[NotificationConstants.PUSH_NOTIFICATION_ACTIVATION_ID] || "";
            }
        }
        
        if (notification.notificationType == NotificationConstants.SYSTEM || notification.notificationType == NotificationConstants.USER) {
            const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
            if (account != null && account != undefined) {
                Logger.logInfo("FirebasePushService", "parseNotification",
                    "Starting Download message.");
                SyncEngine.getMessages()
            }
        } else if (notification.notificationType == NotificationConstants.ATTACHMENT) {}

        return notification;
    }
}

export default FirebasePushService;
