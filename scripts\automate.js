const fs = require('fs');
const {
    exec
} = require('child_process');
const path = require('path')

// Read configuration file
const configPath = './config.json';
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

if (config.automate) {
    const projectPath = path.resolve(__dirname,config.projectPath);
    const pluginName = config.pluginName;
    const rootDirectory = path.resolve(__dirname, '../'); // Navigate up one level from hooks
    const pluginPath = path.resolve(rootDirectory, config.pluginPath);
    // const pluginPath =  path.resolve(__dirname,config.pluginPath);
    // Function to execute shell command
    function executeCommand(command) {
        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    // Reject with stdout to keep executing even if command fails
                    reject(stdout);
                } else {
                    resolve(stdout);
                }
            });
        });
    }

    console.log(`removing plugin ${pluginName} from project ${projectPath}`)
    // Remove plugin (and continue even if fails)
    executeCommand(`cd ${projectPath} && npx cordova plugin remove ${pluginName}`)
        .then((stdout) => {
            console.log('Plugin removal:', stdout);
        })
        .catch((error) => {
            console.error('Plugin removal error:', error);
        })
        .finally(() => {
            // Add plugin after removal attempt
            executeCommand(`cd ${projectPath} && npx cordova plugin add ${pluginPath}`)
                .then((stdout) => {
                    console.log('Plugin added successfully:', stdout);
                })
                .catch((error) => {
                    console.error('Error adding plugin:', error);
                });
        });
} else {
    console.log('\x1b[33m%s\x1b[0m','Automation is disabled in the configuration, Skipping automation ');
    console.log('\x1b[32m%s\x1b[0m','-----------------------------------------------------------------');
    console.log('\x1b[32m%s\x1b[0m', '                    Unvired SDK build successful                ');
    console.log('\x1b[32m%s\x1b[0m','-----------------------------------------------------------------');
}