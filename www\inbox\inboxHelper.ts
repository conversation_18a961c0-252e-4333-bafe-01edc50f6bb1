import { UnviredAccountManager } from "../helper/unviredAccountManager";
import FrameworkHelper from "../helper/frameworkHelper";

declare var Logger: any;

const fileName = "InboxHelper";

export default class InboxHelper {
  private static readonly INBOX_DIR = 'Inbox/'; 

  private static getInboxDirectory(): Promise<string> {
    return new Promise(async (resolve, reject) => {
        const account = UnviredAccountManager.getInstance().getLastLoggedInAccount()
        const userDirectory = await FrameworkHelper.getFolderBasedOnUserId(account.getAccountId());
        const inboxDir = `${userDirectory}${InboxHelper.INBOX_DIR}`;
        window.resolveLocalFileSystemURL(inboxDir, (dir) => {
            Logger.logInfo(fileName, "getInboxDirectory", "Directory exists: " + dir.fullPath);
            resolve(dir.nativeURL);
        }, (error) => {
            Logger.logInfo(fileName, "getInboxDirectory", "Directory did not exist. Error: " + error);
            // Directory did not exist, so creating it.
            Logger.logInfo(fileName, "getInboxDirectory", "Directory did not exist, so creating it: " + inboxDir);
            window.resolveLocalFileSystemURL(userDirectory, (parentDir) => {
                parentDir.getDirectory(InboxHelper.INBOX_DIR, {create: true}, (newDir) => {
                    Logger.logInfo(fileName, "getInboxDirectory", "Directory created: " + newDir.nativeURL);
                    resolve(newDir.nativeURL);
                }, (error) => {
                    Logger.logError(fileName, "getInboxDirectory", "Unable to create Inbox directory. Error: " + error);
                    resolve("");
                })
            }, (error) => {
                Logger.logError(fileName, "getInboxDirectory", "Unable to get Inbox directory. Error: " + error);
                resolve("");
            })
        })
      })
  }

  static addInBoxData(conversationId: string, jsonString: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
        const inboxDir = await this.getInboxDirectory();
        window.resolveLocalFileSystemURL(inboxDir, (dirEntry) => {
            dirEntry.getFile(`${conversationId}.json`, { create: true }, (fileEntry) => {
              fileEntry.createWriter((fileWriter) => {
                      fileWriter.onwriteend = () => {
                          Logger.logInfo(fileName, "addInBoxData", "Write completed");
                          resolve();
                      };
                      fileWriter.onerror = (e) => {
                          Logger.logError(fileName, "addInBoxData", "Write error: " + e);
                          reject(e);
                      };
                      fileWriter.write(jsonString);
              },
              (error) => {
                  Logger.logError(fileName, "addInBoxData", "Create writer error: " + error);
                  reject(error);
              });
            },
            (error) => {
                Logger.logError(fileName, "addInBoxData", "Get file error: " + error);
                reject(error);
            });
        },
        (error) => {
            Logger.logError(fileName, "addInBoxData", "Resolve file system URL error: " + error);
            reject(error);
        });
    });
  }

  static getInBoxData(conversationId: string): Promise<string> {
    return new Promise(async (resolve, reject) => {
      const inboxDir = await this.getInboxDirectory();
      window.resolveLocalFileSystemURL(inboxDir, (dirEntry) => {
        dirEntry.getFile(`${conversationId}.json`, { create: false }, (fileEntry) => {
          fileEntry.file((file) => {
            const reader = new FileReader();
            reader.onloadend = () => {
              Logger.logInfo(fileName, "getInBoxData", "Read completed");
              resolve(reader.result as string);
            };
            reader.onerror = (e) => {
              Logger.logError(fileName, "getInBoxData", "Read error: " + e);
              reject(e);
            };
            reader.readAsText(file);
          }, 
          (error) => {
            Logger.logError(fileName, "getInBoxData", "Get file error: " + error);
            reject(error);
          });
        },
        (error) => {
          Logger.logError(fileName, "getInBoxData", "Get file error: " + error);
          reject(error);
        });
      }, 
      error => {
        Logger.logError(fileName, "getInBoxData", "Resolve file system URL error: " + error);
        reject(error);
      });
    });
  }

  static deleteInBoxData(conversationId: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      const inboxDir = await this.getInboxDirectory();
      window.resolveLocalFileSystemURL(inboxDir, (dirEntry) => {
        dirEntry.getFile(`${conversationId}.json`, { create: false }, (fileEntry) => {
          fileEntry.remove(() => {
              Logger.logInfo(fileName, "deleteInBoxData", "File removed");
              resolve();
            },
            (error) => {
              Logger.logError(fileName, "deleteInBoxData", "Remove file error: " + error);
              reject(error);
            }
          );
        },
        (error) => {
          Logger.logError(fileName, "deleteInBoxData", "Get file error: " + error);
          reject(error);
        });
      },
      (error) => {
        Logger.logError(fileName, "deleteInBoxData", "Resolve file system URL error: " + error);
        reject(error);
      });
    });
  }
}
