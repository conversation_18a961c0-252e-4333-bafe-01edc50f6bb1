import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import FrameworkHelper from "./frameworkHelper";
import { ObjectStatus, SyncStatus } from "./utils";

declare var Logger: any;

export enum UserSettingsFields {
    serverUserId,
    serverPassword,
    unviredUserId,
    unviredPassword,
    serverUrl,
    companyAlias,
    deviceType,
    customDeviceId,
    companyNamespace,
    requestTimeout,
    demoMode,
    domainName,
    loginType,
    notificationTimeout,
    fetchInterval,
    serverCertificateHash,
    currentFrameworkVersion,
    unviredPin,
    unviredId,
    jwtToken
}

export const stringToUserSettingsFields = (fieldName: string): UserSettingsFields => {
    switch(fieldName) {
        case "serverUserId":
            return UserSettingsFields.serverUserId;
        case "serverPassword":
            return UserSettingsFields.serverPassword;
        case "unviredUserId":
            return UserSettingsFields.unviredUserId;
        case "unviredPassword":
            return UserSettingsFields.unviredPassword;
        case "serverUrl":
            return UserSettingsFields.serverUrl;
        case "companyAlias":
            return UserSettingsFields.companyAlias;
        case "deviceType":
            return UserSettingsFields.deviceType;
        case "customDeviceId":
            return UserSettingsFields.customDeviceId;
        case "companyNamespace":
            return UserSettingsFields.companyNamespace;
        case "requestTimeout":
            return UserSettingsFields.requestTimeout;
        case "demoMode":
            return UserSettingsFields.demoMode;
        case "domainName":
            return UserSettingsFields.domainName;
        case "loginType":
            return UserSettingsFields.loginType;
        case "notificationTimeout":
            return UserSettingsFields.notificationTimeout;
        case "fetchInterval":
            return UserSettingsFields.fetchInterval;
        case "serverCertificateHash":
            return UserSettingsFields.serverCertificateHash;
        case "currentFrameworkVersion":
            return UserSettingsFields.currentFrameworkVersion;
        case "unviredPin":
            return UserSettingsFields.unviredPin;
        case "unviredId":
            return UserSettingsFields.unviredId;
        case "jwtToken":
            return UserSettingsFields.jwtToken;
        default:
            return null;
    }
}

export class UserSettingsManager {
    private userSettingsHashtable: any = {};

    private static instance: UserSettingsManager | null = null;

    private constructor() {
        // private constructor to prevent instantiation
    }

    public static getInstance(): UserSettingsManager {
        if (!UserSettingsManager.instance) {
            UserSettingsManager.instance = new UserSettingsManager();
        }
        return UserSettingsManager.instance;
    }

    async setFieldValue(field: UserSettingsFields, fieldValue: any): Promise<void> {
        if (field == null) {
            return;
        }
        const fieldName = UserSettingsFields[field];
        await this.setFieldValueForKey(fieldName, fieldValue);
    }

    async getFieldValue(field: UserSettingsFields): Promise<string> {
        if (field == null) {
            return "";
        }
        const fieldName = UserSettingsFields[field];
        return await this.getFieldValueForKey(fieldName);
    }

    private async setFieldValueForKey(fieldName: string, fieldValue: any): Promise<void> {
        const databaseManager = DatabaseManager.getInstance();
        let userSetting: any;
        try {
            userSetting = await databaseManager.select(DatabaseType.FrameworkDb, "Settings", `fieldName = '${fieldName}'`)
        } catch (e) {}

        if (userSetting == null || userSetting.length == 0) {
            const settingObject = {
                lid: FrameworkHelper.getUUID(),
                timestamp: Date.now(),
                objectStatus: ObjectStatus.global,
                syncStatus: SyncStatus.none,
                fieldName: fieldName,
                fieldValue: `${fieldValue}`
            };
            this.userSettingsHashtable[fieldName] = settingObject;
            await databaseManager.insert(DatabaseType.FrameworkDb, "Settings", settingObject, true);
        } else {
            const settingObject = {
                lid: userSetting[0].lid,
                timestamp: Date.now(),
                objectStatus: userSetting[0].objectStatus,
                syncStatus: userSetting[0].syncStatus,
                fieldName: fieldName,
                fieldValue: `${fieldValue}`
            };
            this.userSettingsHashtable[fieldName] = settingObject;
            await databaseManager.update(DatabaseType.FrameworkDb, "Settings", settingObject, `lid = '${userSetting[0].lid}'`);
        }
    }

    private async getFieldValueForKey(fieldName: string): Promise<string> {
        if (this.userSettingsHashtable && this.userSettingsHashtable[fieldName]) {
            return this.userSettingsHashtable[fieldName].fieldValue;
        }
        const databaseManager = DatabaseManager.getInstance();
        try {
            const userSetting = await databaseManager.select(DatabaseType.FrameworkDb, "Settings", `fieldName = '${fieldName}'`)
            if (userSetting == null || userSetting.length == 0) {
                return "";
            }
            this.userSettingsHashtable[fieldName] = userSetting[0];
            return userSetting[0].fieldValue ?? "";
        } catch (e) {
            Logger.logError("UserSettingsManager", "getFieldValue", `Error while getting settings. Error: ${JSON.stringify(e)}`);
        }
        return "";
    }

    static getDemoModeUserSettings(loginParameters: any): { [key: string]: any } {
        const tempFrameworkSettings = {
            companyAlias: loginParameters.company,
            isDemo: "true",
            deviceType: FrameworkHelper.getFrontendType(),
            loginType: loginParameters.loginType,
            customDeviceId: "demo",
            serverUserId: "demo",
            serverPassword: "demo",
            serverUrl: loginParameters.url,
            currentFrameworkVersion: "DEMO v1.0"
        };
        return tempFrameworkSettings;
    }
}
