import { AuthenticationService } from "../authenticationService";
import OutboxService from "../outbox/outboxService";
import { SyncEngine } from "../syncEngine";
import SettingsHelper from "./settingsHelper";

declare var Logger: any;
export default class GetMessageTimerManager {
    private static instance: GetMessageTimerManager = new GetMessageTimerManager();
    private timer: number | null = null;

    private constructor() {}

    public static getInstance(): GetMessageTimerManager {
        return this.instance;
    }

    public setup(): void {
        this.timer = null;
    }

    public async startTimer(): Promise<void> {
        // Should we start the Timer?
        // 1. We won't if the user hasn't specified the Get Message Polling Interval.
        // 2. We won't start it if it is already running.
        // 3. We won't start if there is no Pending Request.

        // 1.
        if (this.timer !== null) {
            // Timer is already running.
            return;
        }

        // 2.
        // Get the Current Polling Interval.
        const interverInSec: number = await SettingsHelper.getFetchInterval();
        let interval: number = Number(AuthenticationService.instance.loginParameters.autoSyncTime) || 0;

        if (interval <= 0 && interverInSec <= 0) {
            // No Polling Interval specified.
            return;
        }

        // 3.
        // if (!(await this._isAnyRequestPending())) {
        //   // No pending messages.
        //   return;
        // }

        if (interverInSec > 0) {
            interval = interverInSec;
        }

        await Logger.logInfo("GetMessageTimerManager", "callGetMessage", "Starting get message timer.");
        this.timer = window.setInterval(() => {
            this._callGetMessage();
        }, interval * 1000);
    }

    public async stopTimer(): Promise<void> {
        if (this.timer !== null) {
            await Logger.logInfo("GetMessageTimerManager", "stopTimer", "Timer Stopped");
            window.clearInterval(this.timer);
            this.timer = null;
        }
    }

    private async _isAnyRequestPending(): Promise<boolean> {
        const sentItemsCount: number = await SettingsHelper.getSentItemsCount();
        return sentItemsCount > 0;
    }

    private async _callGetMessage(): Promise<void> {
        await Logger.logInfo("GetMessageTimerManager", "callGetMessage", "Timer Elapsed. Calling Get Message");
        await this.stopTimer();
        await this.startTimer();
        await Logger.logInfo("GetMessageTimerManager", "_callGetMessage", "Starting Outbox service.");
        OutboxService.getInstance().start();
        await Logger.logInfo("GetMessageTimerManager", "_callGetMessage", "Starting Download Message service.");
        await new SyncEngine().getMessages();
    }
}